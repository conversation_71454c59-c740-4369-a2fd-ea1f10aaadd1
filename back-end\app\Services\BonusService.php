<?php

namespace App\Services;

use App\Models\Bonus;
use App\Models\BonusClaim;
use App\Models\Transaction;
use App\Models\Mitra;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BonusService
{
    /**
     * Cek dan klaim bonus untuk transaksi
     */
    public function checkAndClaimBonuses(Transaction $transaction): array
    {
        $claimedBonuses = [];

        try {
            // Load transaction items dengan product dan category
            $transaction->load(['items.product.category', 'mitra']);

            if (!$transaction->mitra) {
                return $claimedBonuses;
            }

            // Ambil bonus yang berlaku untuk owner ini
            $ownerId = $this->getOwnerIdFromTransaction($transaction);
            if (!$ownerId) {
                return $claimedBonuses;
            }

            $availableBonuses = Bonus::byOwner($ownerId)
                ->currentlyValid()
                ->with(['category', 'product'])
                ->get();

            foreach ($availableBonuses as $bonus) {
                if ($this->processBonus($bonus, $transaction)) {
                    $claimedBonuses[] = $bonus;
                }
            }

            Log::info("Bonus processing completed", [
                'transaction_id' => $transaction->id,
                'bonuses_claimed' => count($claimedBonuses)
            ]);
        } catch (\Throwable $e) {
            Log::error("Error processing bonuses", [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage()
            ]);
        }

        return $claimedBonuses;
    }

    /**
     * Proses bonus individual
     */
    private function processBonus(Bonus $bonus, Transaction $transaction): bool
    {
        // Cek apakah transaksi memenuhi syarat
        if (!$bonus->isEligibleForTransaction($transaction->items)) {
            return false;
        }

        // Cek apakah sudah pernah klaim bonus ini untuk transaksi ini
        $existingClaim = BonusClaim::where('bonus_id', $bonus->id)
            ->where('transaction_id', $transaction->id)
            ->first();

        if ($existingClaim) {
            return false; // Sudah pernah klaim
        }

        return DB::transaction(function () use ($bonus, $transaction) {
            // Gunakan quota jika bonus tipe quota
            if (!$bonus->useQuota()) {
                return false; // Quota habis
            }

            // Hitung total quantity yang memenuhi syarat
            $qualifyingQuantity = $this->calculateQualifyingQuantity($bonus, $transaction->items);

            // Buat klaim bonus - langsung dianggap claimed (approved)
            BonusClaim::create([
                'bonus_id' => $bonus->id,
                'transaction_id' => $transaction->id,
                'mitra_id' => $transaction->mitra_id,
                'quantity_purchased' => $qualifyingQuantity,
                'bonus_received' => $bonus->bonus_description,
                'status' => 'claimed',
                'claimed_at' => now(),
            ]);

            Log::info("Bonus claimed", [
                'bonus_id' => $bonus->id,
                'transaction_id' => $transaction->id,
                'mitra_id' => $transaction->mitra_id,
                'quantity' => $qualifyingQuantity
            ]);

            return true;
        });
    }

    /**
     * Hitung quantity yang memenuhi syarat bonus
     */
    private function calculateQualifyingQuantity(Bonus $bonus, $transactionItems): int
    {
        $totalQuantity = 0;

        foreach ($transactionItems as $item) {
            // Jika bonus untuk produk spesifik
            if ($bonus->product_id && $item->product_id == $bonus->product_id) {
                $totalQuantity += $item->quantity;
            }
            // Jika bonus untuk kategori
            elseif (!$bonus->product_id && $item->product && $item->product->category_id == $bonus->category_id) {
                $totalQuantity += $item->quantity;
            }
        }

        return $totalQuantity;
    }

    /**
     * Dapatkan owner ID dari transaksi
     */
    private function getOwnerIdFromTransaction(Transaction $transaction): ?int
    {
        $transaction->load('sales');

        if (!$transaction->sales) {
            return null;
        }

        // Jika sales adalah owner
        if ($transaction->sales->role === 'owner') {
            return $transaction->sales->id;
        }

        // Jika sales memiliki owner
        return $transaction->sales->owner_id;
    }

    /**
     * Dapatkan bonus yang tersedia untuk mitra
     */
    public function getAvailableBonusesForMitra(Mitra $mitra): \Illuminate\Database\Eloquent\Collection
    {
        $ownerId = $mitra->owner_id;

        if (!$ownerId) {
            return collect();
        }

        return Bonus::byOwner($ownerId)
            ->currentlyValid()
            ->with(['category', 'product'])
            ->get();
    }

    /**
     * Dapatkan klaim bonus untuk mitra
     */
    public function getBonusClaimsForMitra(Mitra $mitra, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return BonusClaim::byMitra($mitra->id)
            ->with(['bonus.category', 'bonus.product', 'transaction'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Dapatkan statistik bonus untuk owner
     */
    public function getBonusStatsForOwner(int $ownerId, int $days = 30): array
    {
        $startDate = now()->subDays($days);

        $totalBonuses = Bonus::byOwner($ownerId)->count();
        $activeBonuses = Bonus::byOwner($ownerId)->active()->count();

        $totalClaims = BonusClaim::byOwner($ownerId)
            ->where('created_at', '>=', $startDate)
            ->count();

        $pendingClaims = BonusClaim::byOwner($ownerId)
            ->pending()
            ->count();

        $claimedBonuses = BonusClaim::byOwner($ownerId)
            ->claimed()
            ->where('created_at', '>=', $startDate)
            ->count();

        return [
            'total_bonuses' => $totalBonuses,
            'active_bonuses' => $activeBonuses,
            'total_claims' => $totalClaims,
            'pending_claims' => $pendingClaims,
            'claimed_bonuses' => $claimedBonuses,
            'period_days' => $days
        ];
    }

    /**
     * Update status bonus yang kedaluwarsa
     */
    public function updateExpiredBonuses(): int
    {
        $expiredCount = 0;

        // Update bonus periode yang sudah lewat tanggal
        $expiredPeriodBonuses = Bonus::where('type', 'periode')
            ->where('status', 'active')
            ->where('end_date', '<', now())
            ->get();

        foreach ($expiredPeriodBonuses as $bonus) {
            $bonus->update(['status' => 'expired']);
            $expiredCount++;
        }

        // Update bonus quota yang sudah habis
        $expiredQuotaBonuses = Bonus::where('type', 'quota')
            ->where('status', 'active')
            ->whereRaw('quota_used >= quota_total')
            ->get();

        foreach ($expiredQuotaBonuses as $bonus) {
            $bonus->update(['status' => 'expired']);
            $expiredCount++;
        }

        if ($expiredCount > 0) {
            Log::info("Updated expired bonuses", ['count' => $expiredCount]);
        }

        return $expiredCount;
    }

    /**
     * Validasi data bonus sebelum disimpan
     */
    public function validateBonusData(array $data): array
    {
        $errors = [];

        // Validasi berdasarkan type
        if ($data['type'] === 'periode') {
            if (empty($data['start_date']) || empty($data['end_date'])) {
                $errors[] = 'Tanggal mulai dan tanggal sampai wajib diisi untuk bonus periode';
            } elseif ($data['start_date'] >= $data['end_date']) {
                $errors[] = 'Tanggal mulai harus lebih kecil dari tanggal sampai';
            }
        } elseif ($data['type'] === 'quota') {
            if (empty($data['quota_total']) || $data['quota_total'] <= 0) {
                $errors[] = 'Quota total harus lebih besar dari 0';
            }
        }

        // Validasi minimum quantity
        if (empty($data['minimum_quantity']) || $data['minimum_quantity'] <= 0) {
            $errors[] = 'Jumlah pembelian minimum harus lebih besar dari 0';
        }

        return $errors;
    }
}

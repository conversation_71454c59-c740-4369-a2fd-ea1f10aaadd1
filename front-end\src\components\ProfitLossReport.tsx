import React from 'react';
import { apiFetch } from '../services/api';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Download } from 'lucide-react';

// Keep PDF export structure consistent with Reports component
const fetchSettingsSafe = async () => {
  try {
    const s = await apiFetch('/api/settings');
    return {
      biz: s?.business_name || 'Agricultural Cashier System',
      addr: s?.address || '',
      logo: s?.logo_url || '/logo.png',
    };
  } catch {
    return { biz: 'Agricultural Cashier System', addr: '', logo: null };
  }
};

const toDataURL = async (url: string): Promise<string | null> => {
  try {
    const res = await fetch(url, { mode: 'cors' });
    const blob = await res.blob();
    return await new Promise((resolve) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(String(reader.result));
      reader.readAsDataURL(blob);
    });
  } catch {
    return null;
  }
};

const addHeader = async (doc: jsPDF, subtitle: string) => {
  const { biz, addr, logo } = await fetchSettingsSafe();
  if (logo) {
    const dataUrl = await toDataURL(logo);
    if (dataUrl) {
      try { doc.addImage(dataUrl, 'PNG', 40, 24, 36, 36); } catch { }
    }
  }
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(14);
  doc.text(biz, 88, 36);
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(10);
  doc.text(`${subtitle}${addr ? ' — ' + addr : ''}`, 88, 52);
  doc.setDrawColor(200);
  doc.line(40, 60, doc.internal.pageSize.getWidth() - 40, 60);
};

const addSectionImagePages = async (doc: jsPDF, rootId: string) => {
  const root = document.getElementById(rootId);
  if (!root) return;
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const marginTop = 70;
  const marginX = 20;
  const usableWidth = pageWidth - marginX * 2;
  const canvas = await html2canvas(root as HTMLElement, { scale: 2, useCORS: true, backgroundColor: '#ffffff' });
  const imgData = canvas.toDataURL('image/png');
  const imgWidth = usableWidth;
  const imgHeight = (canvas.height * imgWidth) / canvas.width;
  let heightLeft = imgHeight;
  let position = marginTop;
  doc.addImage(imgData, 'PNG', marginX, position, imgWidth, imgHeight);
  heightLeft -= (pageHeight - marginTop);
  while (heightLeft > 0) {
    position = 40;
    doc.addPage();
    doc.addImage(imgData, 'PNG', marginX, position - (imgHeight - heightLeft), imgWidth, imgHeight);
    heightLeft -= pageHeight - 40;
  }
};


type ProfitLoss = {
  revenue: number;
  cogs: number;
  gross_profit: number;
  gross_margin_percent: number;
  period: { start: string; end: string };
};

const toDateInputValue = (d: Date) => d.toISOString().slice(0, 10);
const monthStart = () => { const d = new Date(); d.setDate(1); return d; };
const today = () => new Date();

const formatCurrency = (num: number) =>
  new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', maximumFractionDigits: 0 }).format(num || 0);

const ProfitLossReport: React.FC = () => {
  const [startDate, setStartDate] = React.useState(toDateInputValue(monthStart()));
  const [endDate, setEndDate] = React.useState(toDateInputValue(today()));
  const [loading, setLoading] = React.useState(false);
  const [data, setData] = React.useState<ProfitLoss | null>(null);

  const load = async () => {
    setLoading(true);
    try {
      const q = `?start_date=${startDate}&end_date=${endDate}`;
      const res = await apiFetch(`/api/reports/profit-loss${q}`);
      setData(res);
    } catch (e) { console.error(e); }
    finally { setLoading(false); }
  };

  React.useEffect(() => { load(); /* initial */ }, []);


  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-3">
        <h1 className="text-3xl font-bold text-gray-900">Laporan Laba Rugi</h1>
        <div className="flex items-center flex-wrap gap-3 no-export">
          <input type="date" value={startDate} onChange={(e) => setStartDate(e.target.value)} className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500" />
          <span className="text-gray-500">s/d</span>
          <input type="date" value={endDate} onChange={(e) => setEndDate(e.target.value)} className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500" />
          <button onClick={load} className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">Terapkan</button>
          <button onClick={async () => {
            try {
              const res = await apiFetch(`/api/reports/profit-loss.pdf?start_date=${startDate}&end_date=${endDate}`, { responseType: 'blob' });
              const blob = await (res as Response).blob();
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `laporan-laba-rugi_${startDate}_sd_${endDate}.pdf`;
              document.body.appendChild(a); a.click(); a.remove();
              URL.revokeObjectURL(url);
            } catch (e) { console.error(e); }
          }} className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
            <Download size={18} className="mr-2" />
            Export PDF
          </button>
        </div>
      </div>

      <div id="pl-root" className="space-y-6">
        {loading && (
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">Memuat...</div>
        )}

        {!loading && data && (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                <div className="text-sm text-gray-500">Penjualan (Revenue)</div>
                <div className="text-2xl font-bold text-gray-900">{formatCurrency(data.revenue)}</div>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                <div className="text-sm text-gray-500">HPP (COGS)</div>
                <div className="text-2xl font-bold text-gray-900">{formatCurrency(data.cogs)}</div>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                <div className="text-sm text-gray-500">Laba Kotor</div>
                <div className={`text-2xl font-bold ${data.gross_profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>{formatCurrency(data.gross_profit)}</div>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                <div className="text-sm text-gray-500">Gross Margin</div>
                <div className="text-2xl font-bold text-gray-900">{data.gross_margin_percent}%</div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Ringkasan</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <tbody className="bg-white divide-y divide-gray-200">
                    <tr>
                      <td className="px-6 py-3 text-sm text-gray-600">Periode</td>
                      <td className="px-6 py-3 text-sm text-gray-900">{data.period.start} s/d {data.period.end}</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-3 text-sm text-gray-600">Revenue</td>
                      <td className="px-6 py-3 text-sm text-gray-900">{formatCurrency(data.revenue)}</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-3 text-sm text-gray-600">COGS (HPP)</td>
                      <td className="px-6 py-3 text-sm text-gray-900">{formatCurrency(data.cogs)}</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-3 text-sm text-gray-600">Laba Kotor</td>
                      <td className="px-6 py-3 text-sm text-gray-900">{formatCurrency(data.gross_profit)}</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-3 text-sm text-gray-600">Gross Margin</td>
                      <td className="px-6 py-3 text-sm text-gray-900">{data.gross_margin_percent}%</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default ProfitLossReport;


import React, { useEffect, useState, useMemo } from 'react';
import { apiFetch } from '../services/api';
import { useAuth } from '../context/AuthContext';
import { Eye, Send, Filter, Calendar, Award, TrendingUp, DollarSign } from 'lucide-react';

const formatCurrency = (amount: number) => new Intl.NumberFormat('id-ID', {
  style: 'currency',
  currency: 'IDR',
  minimumFractionDigits: 0
}).format(amount || 0);

const formatPercent = (percent: number) => `${percent.toFixed(1)}%`;

interface MonthData {
  month: number;
  month_name: string;
  debt_total: number;
  paid_total: number;
  percent_paid: number;
  eligible_to_send: boolean;
  eligibility_reason: string;
  already_sent: boolean;
}

interface SummaryData {
  year: number;
  owner_id: number;
  sales_id?: number;
  summary: MonthData[];
}

interface User {
  id: string;
  name: string;
  role: string;
  owner_id?: string;
}

const MonthCard: React.FC<{
  data: MonthData;
  year: number;
  onDetail: () => void;
  onSendBonus: () => void;
}> = ({ data, year, onDetail, onSendBonus }) => {
  const getCardColor = () => {
    if (data.already_sent) return 'border-green-200 bg-green-50';
    if (data.eligible_to_send) return 'border-blue-200 bg-blue-50';
    if (data.percent_paid > 50) return 'border-yellow-200 bg-yellow-50';
    return 'border-gray-200 bg-white';
  };

  const getPercentColor = () => {
    if (data.percent_paid >= 80) return 'text-green-600';
    if (data.percent_paid >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className={`rounded-lg border p-4 ${getCardColor()}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-semibold text-gray-900">{data.month_name}</h3>
        {data.already_sent && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <Award size={12} className="mr-1" />
            Terkirim
          </span>
        )}
      </div>

      <div className="space-y-2 mb-4">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Total Hutang:</span>
          <span className="font-medium">{formatCurrency(data.debt_total)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Sudah Dibayar:</span>
          <span className="font-medium">{formatCurrency(data.paid_total)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Persentase:</span>
          <span className={`font-bold ${getPercentColor()}`}>
            {formatPercent(data.percent_paid)}
          </span>
        </div>
      </div>

      <div className="flex space-x-2">
        <button
          onClick={onDetail}
          className="flex-1 flex items-center justify-center px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
        >
          <Eye size={14} className="mr-1" />
          Detail
        </button>
        <button
          onClick={onSendBonus}
          disabled={!data.eligible_to_send || data.already_sent}
          className={`flex-1 flex items-center justify-center px-3 py-2 text-sm rounded-md ${data.eligible_to_send && !data.already_sent
            ? 'bg-green-600 text-white hover:bg-green-700'
            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          title={data.eligible_to_send ? 'Kirim bonus' : data.eligibility_reason}
        >
          <Send size={14} className="mr-1" />
          Bonus
        </button>
      </div>
    </div>
  );
};

const SalesBonusManagement: React.FC = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [summaryData, setSummaryData] = useState<SummaryData | null>(null);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedSales, setSelectedSales] = useState<string>('');
  const [selectedOwner, setSelectedOwner] = useState<string>('');
  const [salesList, setSalesList] = useState<User[]>([]);
  const [ownersList, setOwnersList] = useState<User[]>([]);

  // Modal states
  const [detailModal, setDetailModal] = useState<{ open: boolean; year: number; month: number } | null>(null);
  const [bonusModal, setBonusModal] = useState<{ open: boolean; year: number; month: number; data: MonthData } | null>(null);
  const [detailData, setDetailData] = useState<any>(null);

  const isSuperAdmin = user?.role === 'superadmin';
  const isOwner = user?.role === 'owner';

  // Generate year options (current year ± 2)
  const yearOptions = useMemo(() => {
    const currentYear = new Date().getFullYear();
    return Array.from({ length: 5 }, (_, i) => currentYear - 2 + i);
  }, []);

  // Load users for filters
  useEffect(() => {
    if (isSuperAdmin) {
      loadOwners();
    } else if (isOwner) {
      loadSales();
    }
  }, [isSuperAdmin, isOwner]);

  // Load sales when owner changes (for superadmin)
  useEffect(() => {
    if (isSuperAdmin && selectedOwner) {
      loadSales();
    }
  }, [selectedOwner, isSuperAdmin]);

  // Load summary when filters change
  useEffect(() => {
    if ((isSuperAdmin && selectedOwner) || isOwner) {
      loadSummary();
    }
  }, [selectedYear, selectedSales, selectedOwner, isSuperAdmin, isOwner]);

  const loadOwners = async () => {
    try {
      const response = await apiFetch('/api/users?role=owner');
      const users = Array.isArray(response?.data) ? response.data : (response || []);
      setOwnersList(users);
      if (users.length > 0 && !selectedOwner) {
        setSelectedOwner(users[0].id);
      }
    } catch (error) {
      console.error('Failed to load owners:', error);
    }
  };

  const loadSales = async () => {
    try {
      const response = await apiFetch('/api/users?role=sales');
      const users = Array.isArray(response?.data) ? response.data : (response || []);
      const filteredSales = isOwner
        ? users.filter((u: User) => u.owner_id === user?.id)
        : users.filter((u: User) => u.owner_id === selectedOwner);
      setSalesList(filteredSales);
    } catch (error) {
      console.error('Failed to load sales:', error);
    }
  };

  const loadSummary = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        year: selectedYear.toString(),
        ...(selectedSales && { sales_id: selectedSales }),
        ...(isSuperAdmin && selectedOwner && { owner_id: selectedOwner })
      });

      const response = await apiFetch(`/api/sales-bonus/summary?${params}`);
      setSummaryData(response);
    } catch (error) {
      console.error('Failed to load summary:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadDetail = async (year: number, month: number) => {
    try {
      const params = new URLSearchParams({
        ...(selectedSales && { sales_id: selectedSales }),
        ...(isSuperAdmin && selectedOwner && { owner_id: selectedOwner })
      });

      const response = await apiFetch(`/api/sales-bonus/${year}/${month}/details?${params}`);
      setDetailData(response);
      setDetailModal({ open: true, year, month });
    } catch (error) {
      console.error('Failed to load detail:', error);
    }
  };

  const sendBonus = async (bonusData: { description: string; amount?: number }) => {
    if (!bonusModal) return;

    try {
      const payload = {
        sales_id: parseInt(selectedSales),
        bonus_description: bonusData.description,
        ...(bonusData.amount && { bonus_amount: bonusData.amount }),
        ...(isSuperAdmin && selectedOwner && { owner_id: parseInt(selectedOwner) })
      };

      await apiFetch(`/api/sales-bonus/${bonusModal.year}/${bonusModal.month}/send`, {
        method: 'POST',
        body: JSON.stringify(payload)
      });

      setBonusModal(null);
      loadSummary(); // Refresh data
      alert('Bonus berhasil dikirim!');
    } catch (error) {
      console.error('Failed to send bonus:', error);
      alert('Gagal mengirim bonus');
    }
  };

  if (!user || (!isSuperAdmin && !isOwner)) {
    return <div className="text-center py-8">Akses ditolak</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Manajemen Bonus Sales</h1>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg border">
        <div className="flex items-center space-x-4">
          <Filter size={20} className="text-gray-500" />

          <div className="flex items-center space-x-2">
            <Calendar size={16} className="text-gray-500" />
            <select
              value={selectedYear}
              onChange={(e) => setSelectedYear(parseInt(e.target.value))}
              className="border rounded px-3 py-2"
            >
              {yearOptions.map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </select>
          </div>

          {isSuperAdmin && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Owner:</span>
              <select
                value={selectedOwner}
                onChange={(e) => {
                  setSelectedOwner(e.target.value);
                  setSelectedSales(''); // Reset sales selection
                }}
                className="border rounded px-3 py-2"
              >
                <option value="">Pilih Owner</option>
                {ownersList.map(owner => (
                  <option key={owner.id} value={owner.id}>{owner.name}</option>
                ))}
              </select>
            </div>
          )}

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Sales:</span>
            <select
              value={selectedSales}
              onChange={(e) => setSelectedSales(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="">Semua Sales</option>
              {salesList.map(sales => (
                <option key={sales.id} value={sales.id}>{sales.name}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      {summaryData && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-blue-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Total Hutang</p>
                <p className="text-lg font-semibold text-gray-900">
                  {formatCurrency(summaryData.summary.reduce((sum, m) => sum + m.debt_total, 0))}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-green-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Total Dibayar</p>
                <p className="text-lg font-semibold text-gray-900">
                  {formatCurrency(summaryData.summary.reduce((sum, m) => sum + m.paid_total, 0))}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center">
              <Award className="h-8 w-8 text-purple-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Bonus Eligible</p>
                <p className="text-lg font-semibold text-gray-900">
                  {summaryData.summary.filter(m => m.eligible_to_send && !m.already_sent).length} Bulan
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Monthly Cards Grid */}
      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Memuat data...</p>
        </div>
      ) : summaryData ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {summaryData.summary.map((monthData) => (
            <MonthCard
              key={monthData.month}
              data={monthData}
              year={selectedYear}
              onDetail={() => loadDetail(selectedYear, monthData.month)}
              onSendBonus={() => setBonusModal({
                open: true,
                year: selectedYear,
                month: monthData.month,
                data: monthData
              })}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-600">
            {isSuperAdmin ? 'Pilih owner untuk melihat data' : 'Tidak ada data untuk ditampilkan'}
          </p>
        </div>
      )}

      {/* Detail Modal */}
      {detailModal && detailData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">
                  Detail {detailData.month_name} {detailModal.year}
                </h3>
                <button
                  onClick={() => setDetailModal(null)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Transaksi Kredit */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Transaksi Kredit</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Tanggal</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Mitra</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Sales</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Total</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {detailData.credit_transactions?.map((tx: any) => (
                        <tr key={tx.id}>
                          <td className="px-4 py-2 text-sm">{tx.id}</td>
                          <td className="px-4 py-2 text-sm">{tx.date}</td>
                          <td className="px-4 py-2 text-sm">{tx.mitra}</td>
                          <td className="px-4 py-2 text-sm">{tx.sales}</td>
                          <td className="px-4 py-2 text-sm">{formatCurrency(tx.total)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Ringkasan per Mitra */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Ringkasan per Mitra</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Mitra</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Total Hutang</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Sudah Dibayar</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Persentase</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Transaksi</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {detailData.mitras_summary?.map((mitra: any) => (
                        <tr key={mitra.mitra_id}>
                          <td className="px-4 py-2 text-sm font-medium">{mitra.mitra_name}</td>
                          <td className="px-4 py-2 text-sm">{formatCurrency(mitra.debt_total)}</td>
                          <td className="px-4 py-2 text-sm">{formatCurrency(mitra.paid_total)}</td>
                          <td className="px-4 py-2 text-sm">
                            <span className={`font-medium ${mitra.percent_paid >= 80 ? 'text-green-600' :
                              mitra.percent_paid >= 50 ? 'text-yellow-600' : 'text-red-600'
                              }`}>
                              {formatPercent(mitra.percent_paid)}
                            </span>
                          </td>
                          <td className="px-4 py-2 text-sm">{mitra.transactions_count}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Send Bonus Modal */}
      {bonusModal && (
        <SendBonusModal
          isOpen={bonusModal.open}
          year={bonusModal.year}
          month={bonusModal.month}
          monthData={bonusModal.data}
          salesName={salesList.find(s => s.id === selectedSales)?.name || 'Sales'}
          onClose={() => setBonusModal(null)}
          onSend={sendBonus}
        />
      )}
    </div>
  );
};

// Send Bonus Modal Component
const SendBonusModal: React.FC<{
  isOpen: boolean;
  year: number;
  month: number;
  monthData: MonthData;
  salesName: string;
  onClose: () => void;
  onSend: (data: { description: string; amount?: number }) => void;
}> = ({ isOpen, year, month, monthData, salesName, onClose, onSend }) => {
  const [description, setDescription] = useState('');
  const [amount, setAmount] = useState<string>('');

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!description.trim()) {
      alert('Deskripsi bonus harus diisi');
      return;
    }

    onSend({
      description: description.trim(),
      ...(amount && { amount: parseInt(amount) })
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg max-w-md w-full mx-4">
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Kirim Bonus Sales</h3>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">✕</button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Ringkasan Periode</h4>
            <div className="text-sm space-y-1">
              <p><span className="text-gray-600">Periode:</span> {monthData.month_name} {year}</p>
              <p><span className="text-gray-600">Sales:</span> {salesName}</p>
              <p><span className="text-gray-600">Total Hutang:</span> {formatCurrency(monthData.debt_total)}</p>
              <p><span className="text-gray-600">Sudah Dibayar:</span> {formatCurrency(monthData.paid_total)}</p>
              <p><span className="text-gray-600">Persentase:</span> <span className="font-medium text-green-600">{formatPercent(monthData.percent_paid)}</span></p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Deskripsi Bonus *
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="Contoh: Bonus pencapaian target pembayaran hutang 85% untuk periode Januari 2024"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nominal Bonus (Opsional)
            </label>
            <input
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="Contoh: 500000"
              min="0"
            />
            <p className="text-xs text-gray-500 mt-1">Kosongkan jika bonus bukan berupa uang</p>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Batal
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              Kirim Bonus
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SalesBonusManagement;

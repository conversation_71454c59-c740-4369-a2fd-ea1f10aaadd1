<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\StockLog;
use App\Models\Bonus;
use App\Models\Reward;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $query = Product::with(['category', 'unit'])->orderBy('name', 'asc');

        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            if ($ownerScopeId) {
                // Produk yang bisa dilihat:
                // 1. Produk yang dibuat oleh owner atau timnya
                // 2. Produk yang dibuat oleh superadmin (tersedia untuk semua)
                $query->join('users as creator', 'products.created_by', '=', 'creator.id')
                    ->where(function ($q) use ($ownerScopeId) {
                        $q->where(function ($ownerScope) use ($ownerScopeId) {
                            // Produk dari owner scope sendiri
                            $ownerScope->where('creator.id', $ownerScopeId)
                                ->orWhere('creator.owner_id', $ownerScopeId);
                        })->orWhere('creator.role', 'superadmin'); // Produk dari superadmin
                    })
                    ->select('products.*');
            } else {
                // Fallback: produk yang dibuat user sendiri + produk dari superadmin
                $query->join('users as creator', 'products.created_by', '=', 'creator.id')
                    ->where(function ($q) use ($user) {
                        $q->where('products.created_by', $user->id)
                            ->orWhere('creator.role', 'superadmin');
                    })
                    ->select('products.*');
            }
        }

        $categoryId = $request->query('category_id');
        $perPage = (int) $request->query('per_page', 20);

        if ($categoryId) {
            $query->where('products.category_id', $categoryId);
        }

        return $query->paginate($perPage);
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'category_id' => ['required', 'exists:categories,id'],
            'price' => ['required', 'integer', 'min:0'],
            'hpp' => ['required', 'integer', 'min:0'],
            'stock' => ['nullable', 'integer', 'min:0'],
            'unit_id' => ['nullable', 'exists:units,id'],
            'description' => ['nullable', 'string']
        ]);

        // Enforce category belongs to same owner scope for non-superadmin
        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            $category = \App\Models\Category::find($data['category_id']);
            $creator = $category ? \App\Models\User::find($category->created_by) : null;
            $creatorOwnerId = $creator ? ($creator->role === 'owner' ? $creator->id : $creator->owner_id) : null;
            if (!$ownerScopeId || !$creatorOwnerId || $ownerScopeId !== $creatorOwnerId) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
        }

        $data['created_by'] = $user->id ?? null;
        $product = Product::create($data);
        return response()->json($product->load(['category', 'unit']), 201);
    }

    public function update(Request $request, Product $product)
    {
        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            $creator = \App\Models\User::find($product->created_by);
            $creatorOwnerId = $creator ? ($creator->role === 'owner' ? $creator->id : $creator->owner_id) : null;
            if (!$ownerScopeId || !$creatorOwnerId || $ownerScopeId !== $creatorOwnerId) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
        }

        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'category_id' => ['required', 'exists:categories,id'],
            'price' => ['required', 'integer', 'min:0'],
            'hpp' => ['required', 'integer', 'min:0'],
            'stock' => ['nullable', 'integer', 'min:0'],
            'unit_id' => ['nullable', 'exists:units,id'],
            'description' => ['nullable', 'string']
        ]);

        // Enforce new category belongs to same owner scope for non-superadmin
        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            $category = \App\Models\Category::find($data['category_id']);
            $creator = $category ? \App\Models\User::find($category->created_by) : null;
            $creatorOwnerId = $creator ? ($creator->role === 'owner' ? $creator->id : $creator->owner_id) : null;
            if (!$ownerScopeId || !$creatorOwnerId || $ownerScopeId !== $creatorOwnerId) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
        }

        $product->update($data);
        return response()->json($product->load(['category', 'unit']));
    }

    public function destroy(Request $request, Product $product)
    {
        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            $creator = \App\Models\User::find($product->created_by);
            $creatorOwnerId = $creator ? ($creator->role === 'owner' ? $creator->id : $creator->owner_id) : null;
            if (!$ownerScopeId || !$creatorOwnerId || $ownerScopeId !== $creatorOwnerId) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
        }

        $product->delete();
        return response()->json(['ok' => true]);
    }

    public function adjustStock(Request $request, Product $product)
    {
        // Authorize by owner scope
        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            $creator = \App\Models\User::find($product->created_by);
            $creatorOwnerId = $creator ? ($creator->role === 'owner' ? $creator->id : $creator->owner_id) : null;
            if (!$ownerScopeId || !$creatorOwnerId || $ownerScopeId !== $creatorOwnerId) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
        }

        $data = $request->validate([
            'type' => ['required', 'in:in,out,adjustment'],
            'quantity' => ['required', 'integer'],
            'reason' => ['nullable', 'string']
        ]);

        return DB::transaction(function () use ($product, $data, $request) {
            $prev = $product->stock;
            $new = $prev;
            if ($data['type'] === 'in') $new = $prev + max(0, $data['quantity']);
            elseif ($data['type'] === 'out') $new = $prev - max(0, $data['quantity']);
            else $new = $prev + $data['quantity'];
            if ($new < 0) $new = 0;
            $product->update(['stock' => $new]);

            StockLog::create([
                'product_id' => $product->id,
                'type' => $data['type'],
                'quantity' => $data['quantity'],
                'previous_stock' => $prev,
                'new_stock' => $new,
                'reason' => $data['reason'] ?? null,
                'created_by' => $request->user()->id ?? null,
            ]);

            return response()->json($product->refresh());
        });
    }

    /**
     * Check if a product has active bonuses or rewards
     */
    public function checkBonusReward(Request $request, Product $product)
    {
        $user = $request->user();

        // Get owner ID for scope checking
        $ownerScopeId = null;
        if ($user) {
            if ($user->role === 'owner') {
                $ownerScopeId = $user->id;
            } elseif ($user->role === 'sales' || $user->role === 'admin_gudang') {
                $ownerScopeId = $user->owner_id;
            }
        }

        $result = [
            'has_bonus' => false,
            'has_reward' => false,
            'bonuses' => [],
            'rewards' => []
        ];

        // Check for active bonuses
        $bonusQuery = Bonus::currentlyValid()
            ->with(['category', 'product'])
            ->where(function ($query) use ($product) {
                $query->where('product_id', $product->id)
                    ->orWhere(function ($q) use ($product) {
                        $q->whereNull('product_id')
                            ->where('category_id', $product->category_id);
                    });
            });

        // Scope bonuses by owner if not superadmin
        if ($ownerScopeId) {
            $bonusQuery->byOwner($ownerScopeId);
        }

        $bonuses = $bonusQuery->get();

        if ($bonuses->isNotEmpty()) {
            $result['has_bonus'] = true;
            $result['bonuses'] = $bonuses->map(function ($bonus) {
                return [
                    'id' => $bonus->id,
                    'name' => $bonus->name,
                    'bonus_description' => $bonus->bonus_description,
                    'minimum_quantity' => $bonus->minimum_quantity,
                    'type' => $bonus->type,
                    'category' => $bonus->category ? $bonus->category->name : null,
                    'product' => $bonus->product ? $bonus->product->name : null
                ];
            });
        }

        // Check for active rewards
        $rewardQuery = Reward::running()
            ->with(['category', 'product'])
            ->where(function ($query) use ($product) {
                $query->where('product_id', $product->id)
                    ->orWhere(function ($q) use ($product) {
                        $q->whereNull('product_id')
                            ->where('category_id', $product->category_id);
                    });
            });

        // Scope rewards by owner if not superadmin
        if ($ownerScopeId) {
            // Reward model uses scopeForOwner (not byOwner)
            $rewardQuery->forOwner($ownerScopeId);
        }

        $rewards = $rewardQuery->get();

        if ($rewards->isNotEmpty()) {
            $result['has_reward'] = true;
            $result['rewards'] = $rewards->map(function ($reward) {
                return [
                    'id' => $reward->id,
                    'name' => $reward->name,
                    'description' => $reward->description,
                    'target_quantity' => $reward->target_quantity,
                    'reward_description' => $reward->reward_description,
                    'category' => $reward->category ? $reward->category->name : null,
                    'product' => $reward->product ? $reward->product->name : null
                ];
            });
        }

        return response()->json($result);
    }
}

export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: 'superadmin' | 'owner' | 'admin_gudang' | 'sales';
  ownerId?: string | null; // optional owner relation for sales/admin_gudang
  createdAt: string;
}

export interface Mitra {
  id: string;
  name: string;
  phone: string;
  address: string;
  creditLimit: number;
  currentDebt: number;
  status: 'active' | 'inactive';
  createdBy: string;
  createdAt: string;
}

export interface Category {
  id: string;
  name: string;
  description: string;
  createdBy: string;
  createdAt: string;
  creator?: {
    id: string;
    name: string;
    role: string;
  };
}

export interface Unit {
  id: number;
  name: string;
  content: string;
  quantity: number;
  base_unit: string;
  description?: string;
  status: string;
  display_text?: string;
  conversion_info?: string;
}

export interface Product {
  id: string;
  name: string;
  categoryId: string;
  category: string;
  price: number;
  hpp?: number;
  stock: number;
  unit: string | Unit; // Support both string (legacy) and Unit object
  unit_id?: number;
  unit_legacy?: string;
  description: string;
  createdBy: string;
  createdAt: string;
}

export interface StockLog {
  id: string;
  productId: string;
  productName: string;
  type: 'in' | 'out' | 'adjustment';
  quantity: number;
  previousStock: number;
  newStock: number;
  reason: string;
  createdBy: string;
  createdAt: string;
}

export interface BonusInfo {
  id: number;
  name: string;
  bonus_description: string;
  minimum_quantity: number;
  type: string;
  category?: string;
  product?: string;
}

export interface RewardInfo {
  id: number;
  name: string;
  description: string;
  target_quantity: number;
  reward_description: string;
  category?: string;
  product?: string;
}

export interface ProductBonusReward {
  has_bonus: boolean;
  has_reward: boolean;
  bonuses: BonusInfo[];
  rewards: RewardInfo[];
}

export interface TransactionItem {
  productId: string;
  productName: string;
  price: number;
  quantity: number;
  discount: number;
  total: number;
  bonusRewardInfo?: ProductBonusReward;
}

export interface Transaction {
  id: string;
  mitraId: string;
  mitraName: string;
  mitraPhone?: string;
  salesId: string;
  salesName: string;
  items: TransactionItem[];
  subtotal: number;
  discountType: 'none' | 'total' | 'per_item';
  discountValue: number;
  discountReason: string;
  total: number;
  paymentMethod: 'cash' | 'credit';
  status: 'pending_owner' | 'pending_gudang' | 'approved' | 'rejected' | 'shipped' | 'delivered';
  dueDate?: string;
  approvedBy?: string;
  rejectionReason?: string;
  shippingDocument?: string;
  deliveryDocument?: string;
  createdAt: string;
  updatedAt: string;
}

export interface TransactionLog {
  id: string;
  transactionId: string;
  userId: string;
  action: 'approve_gudang' | 'reject' | 'ship' | 'deliver';
  previousStatus: string;
  newStatus: string;
  notes: string;
  metadata: {
    user_name: string;
    user_role: string;
    transaction_total: number;
    mitra_name: string;
    timestamp: string;
    [key: string]: any;
  };
  createdAt: string;
  user: {
    id: string;
    name: string;
    role: string;
    email?: string;
  };
}

export interface WhatsAppMessage {
  receiptnumber: string;
  message: string;
  imageurl?: string;
  documenturl?: string;
}
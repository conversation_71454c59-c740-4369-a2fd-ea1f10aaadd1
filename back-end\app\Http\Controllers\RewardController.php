<?php

namespace App\Http\Controllers;

use App\Models\Reward;
use App\Models\RewardProgress;
use App\Services\RewardService;
use Illuminate\Http\Request;

class RewardController extends Controller
{
    protected $rewardService;

    public function __construct(RewardService $rewardService)
    {
        $this->rewardService = $rewardService;
    }

    /**
     * Daftar reward (owner only)
     */
    public function index(Request $request)
    {
        $user = $request->user();

        if (!$user || $user->role !== 'owner') {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $perPage = (int) $request->query('per_page', 20);
        $status = $request->query('status');
        $search = $request->query('search');

        $query = Reward::with(['category', 'product', 'creator'])
            ->forOwner($user->id);

        // Filter berdasarkan status
        if ($status && in_array($status, ['active', 'inactive', 'completed', 'expired'])) {
            $query->where('status', $status);
        }

        // Search
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%")
                    ->orWhereHas('category', function ($cat) use ($search) {
                        $cat->where('name', 'like', "%{$search}%");
                    })
                    ->orWhereHas('product', function ($prod) use ($search) {
                        $prod->where('name', 'like', "%{$search}%");
                    });
            });
        }

        $rewards = $query->orderBy('created_at', 'desc')->paginate($perPage);

        // Tambahkan statistik untuk setiap reward
        $rewards->getCollection()->transform(function ($reward) {
            $reward->statistics = $reward->getStatistics();
            return $reward;
        });

        return response()->json($rewards);
    }

    /**
     * Buat reward baru
     */
    public function store(Request $request)
    {
        $user = $request->user();

        if (!$user || $user->role !== 'owner') {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'category_id' => ['required', 'exists:categories,id'],
            'product_id' => ['required', 'exists:products,id'],
            'target_quantity' => ['required', 'integer', 'min:1'],
            'start_date' => ['required', 'date', 'after_or_equal:today'],
            'end_date' => ['required', 'date', 'after:start_date'],
            'terms_conditions' => ['nullable', 'string'],
            'reward_image' => ['nullable', 'string'],
            'reward_value' => ['nullable', 'numeric', 'min:0'],
            'status' => ['required', 'in:active,inactive']
        ]);

        // Validasi tambahan
        $validationErrors = $this->rewardService->validateRewardData($data);
        if (!empty($validationErrors)) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validationErrors
            ], 422);
        }

        // Validasi kategori dan produk milik owner
        $category = \App\Models\Category::find($data['category_id']);
        if (!$this->isOwnerResource($category, $user->id)) {
            return response()->json(['message' => 'Category not found or not accessible'], 403);
        }

        if ($data['product_id']) {
            $product = \App\Models\Product::find($data['product_id']);
            if (!$product || $product->category_id != $data['category_id']) {
                return response()->json(['message' => 'Product not found or not in selected category'], 422);
            }
        }

        $data['created_by'] = $user->id;
        $reward = Reward::create($data);

        return response()->json($reward->load(['category', 'product']), 201);
    }

    /**
     * Detail reward
     */
    public function show(Request $request, Reward $reward)
    {
        $user = $request->user();

        if (!$user || $user->role !== 'owner') {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        // Cek apakah reward milik owner
        if (!$this->isOwnerReward($reward, $user->id)) {
            return response()->json(['message' => 'Reward not found'], 404);
        }

        $reward->load(['category', 'product', 'creator']);
        $reward->statistics = $reward->getStatistics();

        return response()->json($reward);
    }

    /**
     * Update reward
     */
    public function update(Request $request, Reward $reward)
    {
        $user = $request->user();

        if (!$user || $user->role !== 'owner') {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        // Cek apakah reward milik owner
        if (!$this->isOwnerReward($reward, $user->id)) {
            return response()->json(['message' => 'Reward not found'], 404);
        }

        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'category_id' => ['required', 'exists:categories,id'],
            'product_id' => ['required', 'exists:products,id'],
            'target_quantity' => ['required', 'integer', 'min:1'],
            'start_date' => ['required', 'date'],
            'end_date' => ['required', 'date', 'after:start_date'],
            'terms_conditions' => ['nullable', 'string'],
            'reward_image' => ['nullable', 'string'],
            'reward_value' => ['nullable', 'numeric', 'min:0'],
            'status' => ['required', 'in:active,inactive,completed,expired']
        ]);

        // Validasi tambahan
        $validationErrors = $this->rewardService->validateRewardData($data);
        if (!empty($validationErrors)) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validationErrors
            ], 422);
        }

        // Validasi kategori milik owner
        $category = \App\Models\Category::find($data['category_id']);
        if (!$this->isOwnerResource($category, $user->id)) {
            return response()->json(['message' => 'Category not found or not accessible'], 403);
        }

        if ($data['product_id']) {
            $product = \App\Models\Product::find($data['product_id']);
            if (!$product || $product->category_id != $data['category_id']) {
                return response()->json(['message' => 'Product not found or not in selected category'], 422);
            }
        }

        $reward->update($data);

        return response()->json($reward->load(['category', 'product']));
    }

    /**
     * Hapus reward
     */
    public function destroy(Request $request, Reward $reward)
    {
        $user = $request->user();

        if (!$user || $user->role !== 'owner') {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        // Cek apakah reward milik owner
        if (!$this->isOwnerReward($reward, $user->id)) {
            return response()->json(['message' => 'Reward not found'], 404);
        }

        // Cek apakah ada progress yang sudah berjalan
        $hasProgress = $reward->progresses()->exists();
        if ($hasProgress) {
            return response()->json([
                'message' => 'Cannot delete reward that has participant progress'
            ], 422);
        }

        $reward->delete();

        return response()->json(['message' => 'Reward deleted successfully']);
    }

    /**
     * Leaderboard reward (owner only)
     */
    public function leaderboard(Request $request, Reward $reward)
    {
        $user = $request->user();

        if (!$user || $user->role !== 'owner') {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        // Cek apakah reward milik owner
        if (!$this->isOwnerReward($reward, $user->id)) {
            return response()->json(['message' => 'Reward not found'], 404);
        }

        $limit = (int) $request->query('limit', 10);
        $leaderboard = $this->rewardService->getRewardLeaderboard($reward->id, $limit);

        return response()->json([
            'reward' => [
                'id' => $reward->id,
                'name' => $reward->name,
                'target_quantity' => $reward->target_quantity,
                'category' => $reward->category->name,
                'product' => $reward->product?->name,
                'end_date' => $reward->end_date->format('d/m/Y'),
                'remaining_days' => $reward->remaining_days
            ],
            'leaderboard' => $leaderboard,
            'statistics' => $reward->getStatistics()
        ]);
    }

    /**
     * Progress mitra untuk reward tertentu
     */
    public function mitraProgress(Request $request, int $mitraId)
    {
        $user = $request->user();

        if (!$user || $user->role !== 'owner') {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $progresses = $this->rewardService->getMitraRewardProgress($mitraId, $user->id);

        return response()->json([
            'mitra_id' => $mitraId,
            'progresses' => $progresses
        ]);
    }

    /**
     * Statistik reward untuk owner
     */
    public function stats(Request $request)
    {
        $user = $request->user();

        if (!$user || $user->role !== 'owner') {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $stats = $this->rewardService->getRewardStatsForOwner($user->id);

        return response()->json($stats);
    }

    /**
     * Helper: Cek apakah resource milik owner
     */
    private function isOwnerResource($resource, int $ownerId): bool
    {
        if (!$resource) return false;

        $creator = \App\Models\User::find($resource->created_by);
        if (!$creator) return false;

        // Izinkan resource global (dibuat oleh superadmin) untuk semua owner
        if ($creator->role === 'superadmin') {
            return true;
        }

        $creatorOwnerId = $creator->role === 'owner' ? $creator->id : $creator->owner_id;

        return $creatorOwnerId === $ownerId;
    }

    /**
     * Helper: Cek apakah reward milik owner
     */
    private function isOwnerReward(Reward $reward, int $ownerId): bool
    {
        $creator = \App\Models\User::find($reward->created_by);
        if (!$creator) return false;

        $creatorOwnerId = $creator->role === 'owner' ? $creator->id : $creator->owner_id;

        return $creatorOwnerId === $ownerId;
    }
}

import React, { useState, useEffect } from 'react';
import { useAuth } from './context/AuthContext';
import LoginForm from './components/LoginForm';
import Layout from './components/Layout';
import Dashboard from './components/Dashboard';
import TransactionForm from './components/TransactionForm';
import TransactionList from './components/TransactionList';
import DiscountApproval from './components/DiscountApproval';
import ProductManagement from './components/ProductManagement';
import CategoryManagement from './components/CategoryManagement';
import StockManagement from './components/StockManagement';
import MitraManagement from './components/MitraManagement';
import UserManagement from './components/UserManagement';
import Reports from './components/Reports';
import ProfitLossReport from './components/ProfitLossReport';
import Settings from './components/Settings';
import Receivables from './components/Receivables';
import RewardManagement from './components/RewardManagement';
import BonusManagement from './components/BonusManagement';
import UnitManagement from './components/UnitManagement';

function App() {
  const { user, isLoading } = useAuth();
  const [currentView, setCurrentView] = useState('dashboard');

  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.replace('#', '');
      if (hash) {
        setCurrentView(hash);
      }
      // Always scroll to top when navigating via hash
      window.scrollTo({ top: 0, behavior: 'auto' });
    };

    window.addEventListener('hashchange', handleHashChange);
    handleHashChange(); // Check initial hash and scroll to top

    return () => window.removeEventListener('hashchange', handleHashChange);
  }, []);

  // Also ensure we scroll to top when currentView changes programmatically
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'instant' as ScrollBehavior });
  }, [currentView]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <LoginForm />;
  }

  const renderContent = () => {
    switch (currentView) {
      case 'dashboard':
        return <Dashboard />;

      // Super Admin Routes
      case 'users':
        return user.role === 'superadmin' ? <UserManagement /> : <Dashboard />;
      case 'mitras':
        return (user.role === 'superadmin' || user.role === 'sales') ? <MitraManagement /> : <Dashboard />;
      case 'products':
        return (user.role === 'superadmin' || user.role === 'admin_gudang') ? <ProductManagement /> : <Dashboard />;
      case 'categories':
        return (user.role === 'superadmin' || user.role === 'admin_gudang') ? <CategoryManagement /> : <Dashboard />;
      case 'units':
        return user.role === 'superadmin' ? <UnitManagement /> : <Dashboard />;
      case 'stock':
        return (user.role === 'superadmin' || user.role === 'admin_gudang') ? <StockManagement /> : <Dashboard />;
      case 'stock-history':
        return user.role === 'admin_gudang' ? <StockManagement showHistory={true} /> : <Dashboard />;
      case 'transactions':
        return (user.role === 'superadmin' || user.role === 'admin_gudang') ? <TransactionList userRole={user.role} /> : <Dashboard />;
      case 'reports':
        return (user.role === 'superadmin' || user.role === 'owner' || user.role === 'admin_gudang') ? <Reports /> : <Dashboard />;
      case 'profit-loss':
        return (user.role === 'superadmin' || user.role === 'owner') ? <ProfitLossReport /> : <Dashboard />;

      // Owner Routes
      case 'monitoring':
        return user.role === 'owner' ? <TransactionList userRole="owner" /> : <Dashboard />;
      case 'discount-approval':
        return user.role === 'owner' ? <DiscountApproval /> : <Dashboard />;
      case 'rewards':
        return user.role === 'owner' ? <RewardManagement /> : <Dashboard />;
      case 'bonuses':
        return user.role === 'owner' ? <BonusManagement /> : <Dashboard />;
      case 'settings':
        return user.role === 'owner' ? <Settings /> : <Dashboard />;

      // Sales Routes
      case 'new-transaction':
        return user.role === 'sales' ? <TransactionForm /> : <Dashboard />;
      case 'my-transactions':
        return user.role === 'sales' ? <TransactionList userRole="sales" salesId={user.id} /> : <Dashboard />;
      case 'receivables':
        return (user.role === 'sales' || user.role === 'owner') ? <Receivables /> : <Dashboard />;

      default:
        return <Dashboard />;
    }
  };

  return (
    <Layout>
      {renderContent()}
    </Layout>
  );
}

export default App;
import React, { useState } from 'react';
// import { mockCategories } from '../services/mockData';
import { apiFetch } from '../services/api';
import { Category } from '../types';
// Simple Toast
const Toast: React.FC<{ type: 'success' | 'error'; message: string }> = ({ type, message }) => (
  <div className={`fixed top-6 right-6 z-50 px-4 py-3 rounded-lg shadow-lg text-sm ${type === 'success' ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}`}>
    {message}
  </div>
);

import { Plus, Edit, Trash2, Search, Tag, X } from 'lucide-react';

const CategoryManagement: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [toast, setToast] = useState<{ type: 'success' | 'error'; message: string } | null>(null);

  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  React.useEffect(() => {
    (async () => {
      try {
        const res = await apiFetch('/api/categories');
        const items: any[] = res.data ?? res;
        setCategories(items.map(c => ({
          id: String(c.id),
          name: c.name,
          description: c.description ?? '',
          createdBy: String(c.created_by ?? ''),
          createdAt: c.created_at ?? new Date().toISOString(),
          creator: c.creator ? {
            id: String(c.creator.id),
            name: c.creator.name,
            role: c.creator.role
          } : undefined,
        })));
      } catch (e) {
        console.error(e);
      }
    })();
  }, []);

  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const openModal = (category?: Category) => {
    if (category) {
      // Cek apakah kategori dibuat oleh superadmin
      if (category.creator?.role === 'superadmin') {
        setToast({ type: 'error', message: 'Kategori global tidak dapat diedit' });
        setTimeout(() => setToast(null), 2500);
        return;
      }

      setEditingCategory(category);
      setFormData({
        name: category.name,
        description: category.description
      });
    } else {
      setEditingCategory(null);
      setFormData({
        name: '',
        description: ''
      });
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setEditingCategory(null);
    setFormData({
      name: '',
      description: ''
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      setToast({ type: 'error', message: 'Nama kategori wajib diisi' });
      setTimeout(() => setToast(null), 2500);
      return;
    }

    (async () => {
      try {
        if (editingCategory) {
          const updated = await apiFetch(`/api/categories/${editingCategory.id}`, {
            method: 'PUT',
            body: JSON.stringify(formData)
          });
          setCategories(prev => prev.map(c => c.id === editingCategory.id ? updated : c));
          setToast({ type: 'success', message: 'Kategori berhasil diupdate!' });
          setTimeout(() => setToast(null), 2500);
        } else {
          const created = await apiFetch('/api/categories', {
            method: 'POST',
            body: JSON.stringify(formData)
          });
          setCategories(prev => [created, ...prev]);
          setToast({ type: 'success', message: 'Kategori berhasil ditambahkan!' });
          setTimeout(() => setToast(null), 2500);
        }
        closeModal();
      } catch (e: any) {
        const errorMessage = e.message || 'Gagal menyimpan kategori';
        if (errorMessage.includes('superadmin')) {
          setToast({ type: 'error', message: 'Kategori global tidak dapat diedit' });
        } else {
          setToast({ type: 'error', message: errorMessage });
        }
        setTimeout(() => setToast(null), 2500);
      }
    })();
  };

  const handleDelete = (categoryId: string) => {
    const category = categories.find(c => c.id === categoryId);
    if (category?.creator?.role === 'superadmin') {
      setToast({ type: 'error', message: 'Kategori global tidak dapat dihapus' });
      setTimeout(() => setToast(null), 2500);
      return;
    }

    if (!window.confirm('Apakah Anda yakin ingin menghapus kategori ini?')) return;

    (async () => {
      try {
        await apiFetch(`/api/categories/${categoryId}`, { method: 'DELETE' });
        setCategories(prev => prev.filter(category => category.id !== categoryId));
        setToast({ type: 'success', message: 'Kategori berhasil dihapus!' });
        setTimeout(() => setToast(null), 2500);
      } catch (e: any) {
        const errorMessage = e.message || 'Gagal menghapus kategori';
        if (errorMessage.includes('superadmin')) {
          setToast({ type: 'error', message: 'Kategori global tidak dapat dihapus' });
        } else {
          setToast({ type: 'error', message: errorMessage });
        }
        setTimeout(() => setToast(null), 2500);
      }
    })();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Management Kategori</h1>
        <button
          onClick={() => openModal()}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <Plus size={20} className="mr-2" />
          Tambah Kategori
        </button>
      </div>

      {/* Search */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <div className="relative max-w-md">
          <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
            placeholder="Cari kategori..."
          />
        </div>
      </div>

      {/* Categories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCategories.map((category) => {
          const isCreatedBySuperadmin = category.creator?.role === 'superadmin';

          return (
            <div key={category.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center mb-2">
                    <Tag size={20} className="text-green-600 mr-2 flex-shrink-0" />
                    <h3 className="text-lg font-semibold text-gray-900 truncate" title={category.name}>{category.name}</h3>
                    {isCreatedBySuperadmin && (
                      <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                        Global
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 line-clamp-2 break-words">{category.description}</p>
                </div>
                {!isCreatedBySuperadmin && (
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => openModal(category)}
                      className="p-2 text-blue-600 hover:bg-blue-50 rounded"
                      title="Edit kategori"
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      onClick={() => handleDelete(category.id)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded"
                      title="Hapus kategori"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                )}
              </div>

              <div className="pt-4 border-t border-gray-100">
                <div className="flex items-center justify-between">
                  <div className="text-xs text-gray-500">
                    Dibuat: {new Date(category.createdAt).toLocaleDateString('id-ID')}
                  </div>
                  {isCreatedBySuperadmin && (
                    <div className="text-xs text-blue-600 font-medium">
                      Oleh: {category.creator?.name}
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredCategories.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
          <Tag size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak Ada Kategori</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm
              ? 'Tidak ada kategori yang sesuai dengan pencarian'
              : 'Belum ada kategori ditambahkan'}
          </p>
          {!searchTerm && (
            <button
              onClick={() => openModal()}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <Plus size={16} className="mr-2" />
              Tambah Kategori Pertama
            </button>
          )}
        </div>
      )}

      {/* Category Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-md shadow-lg rounded-lg bg-white">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900">
                {editingCategory ? 'Edit Kategori' : 'Tambah Kategori Baru'}
              </h3>
              <button
                onClick={closeModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={24} />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nama Kategori *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                  placeholder="Masukkan nama kategori"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Deskripsi
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                  rows={4}
                  placeholder="Deskripsi kategori..."
                />
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={closeModal}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Batal
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  {editingCategory ? 'Update Kategori' : 'Tambah Kategori'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default CategoryManagement;
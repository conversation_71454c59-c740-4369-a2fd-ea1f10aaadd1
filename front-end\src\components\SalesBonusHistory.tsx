import React, { useEffect, useState } from 'react';
import { apiFetch } from '../services/api';
import { useAuth } from '../context/AuthContext';
import { Award, Calendar, DollarSign, FileText, TrendingUp } from 'lucide-react';

const formatCurrency = (amount: number) => new Intl.NumberFormat('id-ID', { 
  style: 'currency', 
  currency: 'IDR', 
  minimumFractionDigits: 0 
}).format(amount || 0);

const formatPercent = (percent: number) => `${percent.toFixed(1)}%`;

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

interface BonusPayout {
  id: number;
  owner_id: number;
  sales_id: number;
  year: number;
  month: number;
  month_name: string;
  period: string;
  debt_total: number;
  paid_total: number;
  percent_paid: number;
  bonus_description: string;
  bonus_amount?: number;
  formatted_bonus_amount: string;
  status: string;
  status_name: string;
  created_at: string;
  owner: {
    id: number;
    name: string;
  };
}

interface PaginatedResponse {
  data: BonusPayout[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
}

const StatCard: React.FC<{ 
  title: string; 
  value: React.ReactNode; 
  icon: React.ComponentType<any>;
  color: string;
}> = ({ title, value, icon: Icon, color }) => (
  <div className="bg-white rounded-lg border p-4">
    <div className="flex items-center">
      <Icon className={`h-8 w-8 ${color}`} />
      <div className="ml-3">
        <p className="text-sm font-medium text-gray-500">{title}</p>
        <p className="text-lg font-semibold text-gray-900">{value}</p>
      </div>
    </div>
  </div>
);

const BonusCard: React.FC<{ bonus: BonusPayout }> = ({ bonus }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent': return 'bg-blue-100 text-blue-800';
      case 'received': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-lg border p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center">
          <Award className="h-6 w-6 text-purple-500 mr-2" />
          <div>
            <h3 className="font-semibold text-gray-900">{bonus.period}</h3>
            <p className="text-sm text-gray-600">dari {bonus.owner.name}</p>
          </div>
        </div>
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(bonus.status)}`}>
          {bonus.status_name}
        </span>
      </div>

      <div className="space-y-3 mb-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Total Hutang:</span>
            <p className="font-medium">{formatCurrency(bonus.debt_total)}</p>
          </div>
          <div>
            <span className="text-gray-600">Sudah Dibayar:</span>
            <p className="font-medium">{formatCurrency(bonus.paid_total)}</p>
          </div>
        </div>
        
        <div className="flex justify-between items-center text-sm">
          <span className="text-gray-600">Persentase Pembayaran:</span>
          <span className="font-bold text-green-600">{formatPercent(bonus.percent_paid)}</span>
        </div>

        {bonus.bonus_amount && (
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">Nominal Bonus:</span>
            <span className="font-bold text-purple-600">{bonus.formatted_bonus_amount}</span>
          </div>
        )}
      </div>

      <div className="border-t pt-4">
        <p className="text-sm text-gray-600 mb-2">Deskripsi Bonus:</p>
        <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">{bonus.bonus_description}</p>
      </div>

      <div className="mt-4 text-xs text-gray-500">
        Dikirim pada {formatDate(bonus.created_at)}
      </div>
    </div>
  );
};

const SalesBonusHistory: React.FC = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [bonuses, setBonuses] = useState<BonusPayout[]>([]);
  const [pagination, setPagination] = useState<{
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  } | null>(null);
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    loadBonusHistory();
  }, [currentPage]);

  const loadBonusHistory = async () => {
    setLoading(true);
    try {
      const response: PaginatedResponse = await apiFetch(`/api/sales-bonus/history?page=${currentPage}&per_page=10`);
      setBonuses(response.data || []);
      setPagination({
        current_page: response.current_page,
        last_page: response.last_page,
        per_page: response.per_page,
        total: response.total
      });
    } catch (error) {
      console.error('Failed to load bonus history:', error);
    } finally {
      setLoading(false);
    }
  };

  // Calculate summary stats
  const totalBonusAmount = bonuses.reduce((sum, bonus) => sum + (bonus.bonus_amount || 0), 0);
  const totalBonusCount = bonuses.length;
  const avgPercentPaid = bonuses.length > 0 
    ? bonuses.reduce((sum, bonus) => sum + bonus.percent_paid, 0) / bonuses.length 
    : 0;

  if (!user || user.role !== 'sales') {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Akses ditolak. Halaman ini hanya untuk role sales.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Riwayat Bonus Saya</h1>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <StatCard
          title="Total Bonus Diterima"
          value={totalBonusCount}
          icon={Award}
          color="text-purple-500"
        />
        <StatCard
          title="Total Nominal Bonus"
          value={formatCurrency(totalBonusAmount)}
          icon={DollarSign}
          color="text-green-500"
        />
        <StatCard
          title="Rata-rata Persentase"
          value={formatPercent(avgPercentPaid)}
          icon={TrendingUp}
          color="text-blue-500"
        />
      </div>

      {/* Bonus History */}
      <div className="bg-white rounded-lg border">
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Daftar Bonus</h2>
            {pagination && (
              <p className="text-sm text-gray-600">
                Menampilkan {bonuses.length} dari {pagination.total} bonus
              </p>
            )}
          </div>
        </div>

        <div className="p-6">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Memuat riwayat bonus...</p>
            </div>
          ) : bonuses.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {bonuses.map((bonus) => (
                <BonusCard key={bonus.id} bonus={bonus} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Award className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Belum Ada Bonus</h3>
              <p className="text-gray-600">
                Anda belum menerima bonus apapun. Tingkatkan performa pembayaran hutang mitra untuk mendapatkan bonus!
              </p>
            </div>
          )}
        </div>

        {/* Pagination */}
        {pagination && pagination.last_page > 1 && (
          <div className="px-6 py-4 border-t">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Halaman {pagination.current_page} dari {pagination.last_page}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={pagination.current_page <= 1}
                  className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Sebelumnya
                </button>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(pagination.last_page, prev + 1))}
                  disabled={pagination.current_page >= pagination.last_page}
                  className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Selanjutnya
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Info Box */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <FileText className="h-5 w-5 text-blue-500 mt-0.5 mr-3" />
          <div>
            <h3 className="text-sm font-medium text-blue-900 mb-1">Informasi Bonus Sales</h3>
            <p className="text-sm text-blue-700">
              Bonus diberikan ketika persentase pembayaran hutang mitra pada periode tertentu mencapai lebih dari 80% 
              dan sudah melewati 3 bulan dari periode tersebut. Bonus dapat berupa nominal uang atau bentuk apresiasi lainnya.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalesBonusHistory;

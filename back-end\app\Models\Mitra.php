<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Mitra extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'phone', 'address', 'credit_limit', 'current_debt', 'status', 'created_by', 'owner_id'];

    /**
     * <PERSON><PERSON><PERSON> ke Owner
     */
    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }
}

export const API_BASE = (import.meta.env.VITE_API_URL as string) || 'https://api.agricash.senusatour.com';

export async function apiFetch(path: string, options: RequestInit & { responseType?: 'json' | 'blob' } = {}) {
  const token = localStorage.getItem('token');

  // Build headers conditionally: when sending FormData, let the browser set Content-Type
  const isFormData = typeof FormData !== 'undefined' && options.body instanceof FormData;
  const headers: Record<string, string> = {
    ...(isFormData ? {} : { 'Content-Type': 'application/json' }),
    ...(options.responseType === 'blob' ? {} : { 'Accept': 'application/json' }),
    ...(options.headers as Record<string, string> | undefined),
  };
  if (token) headers['Authorization'] = `Bearer ${token}`;

  const res = await fetch(`${API_BASE}${path}`, { ...options, headers });
  if (!res.ok) {
    if (options.responseType === 'blob') {
      throw new Error('Request failed');
    }
    let message = 'Request failed';
    try { const body = await res.json(); message = body.message || message; } catch { }
    throw new Error(message);
  }

  if (options.responseType === 'blob') {
    return res as any; // caller will await res.blob()
  }

  const contentType = res.headers.get('content-type') || '';
  if (!contentType.includes('application/json')) {
    return {} as any;
  }
  return res.json();
}


<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_bonus_payouts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('owner_id')->constrained('users')->cascadeOnDelete(); // Owner yang memberikan bonus
            $table->foreignId('sales_id')->constrained('users')->cascadeOnDelete(); // Sales yang menerima bonus
            $table->year('year'); // Tahun periode bonus (misal 2024)
            $table->tinyInteger('month'); // Bulan periode bonus (1-12)
            $table->unsignedBigInteger('debt_total')->default(0); // Total hutang yang tercipta pada bulan tersebut
            $table->unsignedBigInteger('paid_total')->default(0); // Total yang sudah dibayar dari hutang bulan tersebut
            $table->decimal('percent_paid', 5, 2)->default(0); // Persentase yang sudah dibayar (0.00-100.00)
            $table->text('bonus_description'); // Deskripsi bonus yang diberikan
            $table->unsignedBigInteger('bonus_amount')->nullable(); // Nominal bonus dalam rupiah (opsional)
            $table->enum('status', ['sent', 'received', 'cancelled'])->default('sent'); // Status bonus
            $table->foreignId('created_by')->constrained('users')->cascadeOnDelete(); // User yang membuat record ini
            $table->timestamps();
            
            // Index untuk performa
            $table->index(['owner_id', 'year', 'month']);
            $table->index(['sales_id', 'year', 'month']);
            $table->index(['year', 'month']);
            $table->index(['status']);
            
            // Constraint: satu bonus per sales per bulan per tahun
            $table->unique(['owner_id', 'sales_id', 'year', 'month']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales_bonus_payouts');
    }
};

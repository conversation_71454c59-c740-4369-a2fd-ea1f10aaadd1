<?php

namespace App\Http\Controllers;

use App\Models\Category;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $query = Category::with('creator:id,name,role')->orderBy('name', 'asc');

        if ($user && $user->role !== 'superadmin') {
            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            if ($ownerScopeId) {
                // Kategori yang bisa dilihat:
                // 1. Kategori yang dibuat oleh owner atau timnya
                // 2. Kategori yang dibuat oleh superadmin (tersedia untuk semua)
                $query->join('users as creator', 'categories.created_by', '=', 'creator.id')
                    ->where(function ($q) use ($ownerScopeId) {
                        $q->where(function ($ownerScope) use ($ownerScopeId) {
                            // Kategori dari owner scope sendiri
                            $ownerScope->where('creator.id', $ownerScopeId)
                                ->orWhere('creator.owner_id', $ownerScopeId);
                        })->orWhere('creator.role', 'superadmin'); // Kategori dari superadmin
                    })
                    ->select('categories.*');
            } else {
                // Fallback: kategori yang dibuat user sendiri + kategori dari superadmin
                $query->join('users as creator', 'categories.created_by', '=', 'creator.id')
                    ->where(function ($q) use ($user) {
                        $q->where('categories.created_by', $user->id)
                            ->orWhere('creator.role', 'superadmin');
                    })
                    ->select('categories.*');
            }
        }

        return $query->paginate(20);
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string']
        ]);
        $data['created_by'] = $request->user()->id ?? null;
        $category = Category::create($data);
        return response()->json($category, 201);
    }

    public function update(Request $request, Category $category)
    {
        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $creator = \App\Models\User::find($category->created_by);

            // Jika kategori dibuat oleh superadmin, tidak bisa diedit oleh non-superadmin
            if ($creator && $creator->role === 'superadmin') {
                return response()->json(['message' => 'Cannot edit category created by superadmin'], 403);
            }

            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            $creatorOwnerId = $creator ? ($creator->role === 'owner' ? $creator->id : $creator->owner_id) : null;
            if (!$ownerScopeId || !$creatorOwnerId || $ownerScopeId !== $creatorOwnerId) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
        }

        $data = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string']
        ]);
        $category->update($data);
        return response()->json($category);
    }

    public function destroy(Request $request, Category $category)
    {
        $user = $request->user();
        if ($user && $user->role !== 'superadmin') {
            $creator = \App\Models\User::find($category->created_by);

            // Jika kategori dibuat oleh superadmin, tidak bisa dihapus oleh non-superadmin
            if ($creator && $creator->role === 'superadmin') {
                return response()->json(['message' => 'Cannot delete category created by superadmin'], 403);
            }

            $ownerScopeId = $user->role === 'owner' ? $user->id : $user->owner_id;
            $creatorOwnerId = $creator ? ($creator->role === 'owner' ? $creator->id : $creator->owner_id) : null;
            if (!$ownerScopeId || !$creatorOwnerId || $ownerScopeId !== $creatorOwnerId) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
        }

        $category->delete();
        return response()->json(['ok' => true]);
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class SalesBonusPayout extends Model
{
    use HasFactory;

    protected $fillable = [
        'owner_id',
        'sales_id',
        'year',
        'month',
        'debt_total',
        'paid_total',
        'percent_paid',
        'bonus_description',
        'bonus_amount',
        'status',
        'created_by'
    ];

    protected $casts = [
        'year' => 'integer',
        'month' => 'integer',
        'debt_total' => 'integer',
        'paid_total' => 'integer',
        'percent_paid' => 'decimal:2',
        'bonus_amount' => 'integer',
    ];

    /**
     * Relasi ke Owner
     */
    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * Relasi ke Sales
     */
    public function sales()
    {
        return $this->belongsTo(User::class, 'sales_id');
    }

    /**
     * <PERSON>lasi ke User yang membuat record
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope untuk filter berdasarkan owner
     */
    public function scopeByOwner($query, $ownerId)
    {
        return $query->where('owner_id', $ownerId);
    }

    /**
     * Scope untuk filter berdasarkan sales
     */
    public function scopeBySales($query, $salesId)
    {
        return $query->where('sales_id', $salesId);
    }

    /**
     * Scope untuk filter berdasarkan tahun
     */
    public function scopeByYear($query, $year)
    {
        return $query->where('year', $year);
    }

    /**
     * Scope untuk filter berdasarkan bulan
     */
    public function scopeByMonth($query, $month)
    {
        return $query->where('month', $month);
    }

    /**
     * Scope untuk status tertentu
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Accessor untuk nama bulan
     */
    public function getMonthNameAttribute(): string
    {
        $monthNames = [
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        ];

        return $monthNames[$this->month] ?? 'Unknown';
    }

    /**
     * Accessor untuk periode (format: "Januari 2024")
     */
    public function getPeriodAttribute(): string
    {
        return $this->month_name . ' ' . $this->year;
    }

    /**
     * Accessor untuk status yang user-friendly
     */
    public function getStatusNameAttribute(): string
    {
        $statusNames = [
            'sent' => 'Dikirim',
            'received' => 'Diterima',
            'cancelled' => 'Dibatalkan'
        ];

        return $statusNames[$this->status] ?? $this->status;
    }

    /**
     * Check apakah bonus untuk periode tertentu sudah eligible untuk dikirim
     * Rule: >80% paid dan sudah >3 bulan dari periode tersebut
     */
    public static function isEligibleToSend($year, $month, $percentPaid): bool
    {
        // Rule 1: Persentase pembayaran harus > 80%
        if ($percentPaid <= 80) {
            return false;
        }

        // Rule 2: Sudah lebih dari 3 bulan dari periode tersebut
        $periodDate = Carbon::create($year, $month, 1);
        $eligibleDate = $periodDate->copy()->addMonths(4)->startOfMonth(); // 4 bulan kemudian (>3 bulan)
        $now = Carbon::now();

        return $now->gte($eligibleDate);
    }

    /**
     * Check apakah bonus untuk periode tertentu sudah pernah dikirim
     */
    public static function alreadySent($ownerId, $salesId, $year, $month): bool
    {
        return self::where('owner_id', $ownerId)
            ->where('sales_id', $salesId)
            ->where('year', $year)
            ->where('month', $month)
            ->exists();
    }

    /**
     * Format bonus amount untuk display
     */
    public function getFormattedBonusAmountAttribute(): string
    {
        if (!$this->bonus_amount) {
            return '-';
        }

        return 'Rp ' . number_format($this->bonus_amount, 0, ',', '.');
    }
}

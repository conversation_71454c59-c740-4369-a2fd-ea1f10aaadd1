import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Gift, Eye, Trophy, Target, Calendar, Users, TrendingUp } from 'lucide-react';
import { apiFetch } from '../services/api';
import { safeNumber, safeToFixed, safeToLocaleString } from '../utils/numberUtils';
import RewardLeaderboard from './RewardLeaderboard';

interface Reward {
  id: number;
  name: string;
  description?: string;
  category: {
    id: number;
    name: string;
  };
  product?: {
    id: number;
    name: string;
  };
  target_quantity: number;
  start_date: string;
  end_date: string;
  status: 'active' | 'inactive' | 'completed' | 'expired';
  status_name: string;
  terms_conditions?: string;
  reward_image?: string;
  reward_value?: number;
  statistics: {
    total_participants: number;
    completed_participants: number;
    completion_rate: number;
    total_sold_quantity: number;
    overall_progress: number;
    remaining_days: number;
    is_valid: boolean;
    is_expired: boolean;
  };
  created_at: string;
}

interface Category {
  id: number;
  name: string;
}

interface Product {
  id: number;
  name: string;
  category_id: number;
}

interface RewardStats {
  total_rewards: number;
  active_rewards: number;
  completed_rewards: number;
  expired_rewards: number;
  total_participants: number;
  completed_participants: number;
  overall_completion_rate: number;
  most_popular_reward?: {
    id: number;
    name: string;
    participants: number;
  };
  best_performing_reward?: {
    id: number;
    name: string;
    completion_rate: number;
  };
}

const RewardManagement: React.FC = () => {
  const [rewards, setRewards] = useState<Reward[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [stats, setStats] = useState<RewardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [showStats, setShowStats] = useState(false);
  const [showLeaderboard, setShowLeaderboard] = useState(false);
  const [selectedReward, setSelectedReward] = useState<Reward | null>(null);
  const [editingReward, setEditingReward] = useState<Reward | null>(null);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category_id: '',
    product_id: '',
    target_quantity: '',
    start_date: '',
    end_date: '',
    terms_conditions: '',
    reward_value: '',
    status: 'active' as 'active' | 'inactive'
  });

  const [filters, setFilters] = useState({
    status: '',
    search: ''
  });

  useEffect(() => {
    fetchRewards();
    fetchCategories();
    fetchStats();
  }, [filters]);

  useEffect(() => {
    if (formData.category_id) {
      fetchProducts(parseInt(formData.category_id));
    } else {
      setProducts([]);
      setFormData(prev => ({ ...prev, product_id: '' }));
    }
  }, [formData.category_id]);

  const fetchRewards = async () => {
    try {
      const params = new URLSearchParams();

      if (filters.status) params.append('status', filters.status);
      if (filters.search) params.append('search', filters.search);

      const data = await apiFetch(`/api/rewards?${params.toString()}`);
      setRewards(data.data || []);
    } catch (error) {
      console.error('Error fetching rewards:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const data = await apiFetch('/api/categories');
      setCategories(data.data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchProducts = async (categoryId: number) => {
    try {
      const data = await apiFetch(`/api/products?category_id=${categoryId}`);
      setProducts(data.data || []);
    } catch (error) {
      console.error('Error fetching products:', error);
    }
  };

  const fetchStats = async () => {
    try {
      const data = await apiFetch('/api/rewards-stats');
      setStats(data);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const url = editingReward ? `/api/rewards/${editingReward.id}` : '/api/rewards';
      const method = editingReward ? 'PUT' : 'POST';

      const submitData = {
        ...formData,
        target_quantity: parseInt(formData.target_quantity),
        reward_value: formData.reward_value ? parseFloat(formData.reward_value) : null,
        product_id: parseInt(formData.product_id)
      };

      await apiFetch(url, {
        method,
        body: JSON.stringify(submitData)
      });

      await fetchRewards();
      await fetchStats();
      resetForm();
      alert(editingReward ? 'Reward berhasil diperbarui!' : 'Reward berhasil dibuat!');
    } catch (error) {
      console.error('Error saving reward:', error);
      alert('Terjadi kesalahan saat menyimpan reward');
    }
  };

  const handleEdit = (reward: Reward) => {
    // Normalisasi tanggal untuk input type="date" (YYYY-MM-DD)
    const toDateInput = (val: any): string => {
      if (!val) return '';
      const s = String(val);
      if (/^\d{4}-\d{2}-\d{2}$/.test(s)) return s; // sudah sesuai
      if (s.includes('T')) return s.slice(0, 10); // ISO 8601
      if (s.includes(' ')) return s.split(' ')[0]; // "YYYY-MM-DD HH:MM:SS"
      try {
        return new Date(s).toISOString().slice(0, 10);
      } catch {
        return '';
      }
    };

    setEditingReward(reward);
    setFormData({
      name: reward.name,
      description: reward.description || '',
      category_id: reward.category.id.toString(),
      product_id: reward.product?.id.toString() || '',
      target_quantity: reward.target_quantity.toString(),
      start_date: toDateInput(reward.start_date),
      end_date: toDateInput(reward.end_date),
      terms_conditions: reward.terms_conditions || '',
      reward_value: reward.reward_value?.toString() || '',
      status: reward.status
    });
    setShowForm(true);
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Apakah Anda yakin ingin menghapus reward ini?')) return;

    try {
      await apiFetch(`/api/rewards/${id}`, {
        method: 'DELETE'
      });

      await fetchRewards();
      await fetchStats();
      alert('Reward berhasil dihapus!');
    } catch (error) {
      console.error('Error deleting reward:', error);
      alert('Terjadi kesalahan saat menghapus reward');
    }
  };

  const handleViewLeaderboard = (reward: Reward) => {
    setSelectedReward(reward);
    setShowLeaderboard(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      category_id: '',
      product_id: '',
      target_quantity: '',
      start_date: '',
      end_date: '',
      terms_conditions: '',
      reward_value: '',
      status: 'active'
    });
    setEditingReward(null);
    setShowForm(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'expired': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Manajemen Reward</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowStats(!showStats)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
          >
            <TrendingUp className="h-4 w-4" />
            <span>{showStats ? 'Sembunyikan' : 'Lihat'} Statistik</span>
          </button>
          <button
            onClick={() => setShowForm(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Tambah Reward</span>
          </button>
        </div>
      </div>

      {/* Statistics */}
      {showStats && stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Gift className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Reward</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.total_rewards}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Target className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Reward Aktif</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.active_rewards}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Users className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Partisipan</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.total_participants}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Trophy className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Tingkat Penyelesaian</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.overall_completion_rate}%</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            >
              <option value="">Semua Status</option>
              <option value="active">Aktif</option>
              <option value="inactive">Tidak Aktif</option>
              <option value="completed">Selesai</option>
              <option value="expired">Kedaluwarsa</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Pencarian</label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              placeholder="Cari nama reward, kategori, atau produk..."
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            />
          </div>
        </div>
      </div>

      {/* Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingReward ? 'Edit Reward' : 'Tambah Reward Baru'}
              </h3>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Nama Reward</label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Paket Haji, Motor, dll"
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Nilai Reward (Opsional)</label>
                    <input
                      type="number"
                      value={formData.reward_value}
                      onChange={(e) => setFormData({ ...formData, reward_value: e.target.value })}
                      placeholder="Nilai dalam rupiah"
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      min="0"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Deskripsi</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    rows={3}
                    placeholder="Deskripsi reward..."
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Kategori Produk</label>
                    <select
                      value={formData.category_id}
                      onChange={(e) => setFormData({ ...formData, category_id: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      required
                    >
                      <option value="">Pilih Kategori</option>
                      {categories.map(category => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Produk Spesifik</label>
                    <select
                      value={formData.product_id}
                      onChange={(e) => setFormData({ ...formData, product_id: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      disabled={!formData.category_id}
                      required
                    >
                      <option value="">Pilih produk</option>
                      {products.map(product => (
                        <option key={product.id} value={product.id}>
                          {product.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Target Penjualan</label>
                    <input
                      type="number"
                      value={formData.target_quantity}
                      onChange={(e) => setFormData({ ...formData, target_quantity: e.target.value })}
                      placeholder="1000"
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      min="1"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Tanggal Mulai</label>
                    <input
                      type="date"
                      value={formData.start_date}
                      onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Tanggal Berakhir</label>
                    <input
                      type="date"
                      value={formData.end_date}
                      onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Syarat dan Ketentuan</label>
                  <textarea
                    value={formData.terms_conditions}
                    onChange={(e) => setFormData({ ...formData, terms_conditions: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    rows={3}
                    placeholder="Syarat dan ketentuan reward..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value as 'active' | 'inactive' })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="active">Aktif</option>
                    <option value="inactive">Tidak Aktif</option>
                  </select>
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Batal
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    {editingReward ? 'Perbarui' : 'Simpan'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Rewards Table */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Daftar Reward</h3>

          {rewards.length === 0 ? (
            <div className="text-center py-8">
              <Gift className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Belum ada reward</h3>
              <p className="mt-1 text-sm text-gray-500">Mulai dengan membuat reward pertama Anda.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Reward
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Target & Progress
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Periode
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Partisipan
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Aksi
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {rewards.map((reward) => (
                    <tr key={reward.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{reward.name}</div>
                          <div className="text-sm text-gray-500">{reward.category.name}</div>
                          {reward.product && (
                            <div className="text-xs text-gray-400">{reward.product.name}</div>
                          )}
                          {reward.reward_value && (
                            <div className="text-xs text-green-600">{formatCurrency(reward.reward_value)}</div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm text-gray-900">
                            Target: {safeToLocaleString(reward.target_quantity)} unit
                          </div>
                          <div className="text-sm text-gray-500">
                            Terjual: {safeToLocaleString(reward.statistics.total_sold_quantity)} unit
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${Math.min(100, safeNumber(reward.statistics.overall_progress))}%` }}
                            ></div>
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            {safeToFixed(reward.statistics.overall_progress, 1)}%
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm text-gray-900">
                            {new Date(reward.start_date).toLocaleDateString()} - {new Date(reward.end_date).toLocaleDateString()}
                          </div>
                          <div className="text-sm text-gray-500">
                            {reward.statistics.remaining_days > 0
                              ? `${reward.statistics.remaining_days} hari tersisa`
                              : 'Berakhir'
                            }
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm text-gray-900">
                            {reward.statistics.total_participants} mitra
                          </div>
                          <div className="text-sm text-gray-500">
                            {reward.statistics.completed_participants} selesai ({reward.statistics.completion_rate}%)
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(reward.status)}`}>
                          {reward.status_name || (reward.status === 'active' ? 'Aktif' : reward.status === 'inactive' ? 'Tidak Aktif' : reward.status === 'completed' ? 'Selesai' : reward.status === 'expired' ? 'Kedaluwarsa' : reward.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleViewLeaderboard(reward)}
                            className="text-purple-600 hover:text-purple-900"
                            title="Lihat Leaderboard"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleEdit(reward)}
                            className="text-indigo-600 hover:text-indigo-900"
                            title="Edit"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(reward.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Hapus"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Leaderboard Modal */}
      {selectedReward && (
        <RewardLeaderboard
          reward={{
            id: selectedReward.id,
            name: selectedReward.name,
            target_quantity: selectedReward.target_quantity,
            category: selectedReward.category.name,
            product: selectedReward.product?.name,
            end_date: selectedReward.end_date,
            remaining_days: selectedReward.statistics.remaining_days
          }}
          isOpen={showLeaderboard}
          onClose={() => {
            setShowLeaderboard(false);
            setSelectedReward(null);
          }}
        />
      )}
    </div>
  );
};

export default RewardManagement;

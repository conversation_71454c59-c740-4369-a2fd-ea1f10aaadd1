<?php

namespace App\Http\Controllers;

use App\Models\SalesBonusPayout;
use App\Models\Transaction;
use App\Models\ReceivablePayment;
use App\Models\User;
use Illuminate\Http\Request;
use Carbon\Carbon;

class SalesBonusController extends Controller
{
    /**
     * Get summary data untuk 12 bulan dalam tahun tertentu
     * GET /api/sales-bonus/summary?year=2024&sales_id=optional
     */
    public function summary(Request $request)
    {
        $user = $request->user();

        // Hanya superadmin dan owner yang bisa mengakses
        if (!$user || !in_array($user->role, ['superadmin', 'owner'])) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $request->validate([
            'year' => ['required', 'integer', 'min:2020', 'max:2030'],
            'sales_id' => ['nullable', 'integer', 'exists:users,id'],
            'owner_id' => ['nullable', 'integer', 'exists:users,id'] // untuk superadmin
        ]);

        $year = $request->input('year');
        $salesId = $request->input('sales_id');
        $ownerId = $request->input('owner_id');

        // Determine scope berdasarkan role
        if ($user->role === 'owner') {
            $ownerId = $user->id;
        } elseif ($user->role === 'superadmin' && !$ownerId) {
            return response()->json(['message' => 'owner_id required for superadmin'], 400);
        }

        $summary = [];

        for ($month = 1; $month <= 12; $month++) {
            $monthData = $this->calculateMonthData($ownerId, $salesId, $year, $month);
            $summary[] = $monthData;
        }

        return response()->json([
            'year' => $year,
            'owner_id' => $ownerId,
            'sales_id' => $salesId,
            'summary' => $summary
        ]);
    }

    /**
     * Get detail data untuk bulan tertentu
     * GET /api/sales-bonus/{year}/{month}/details?sales_id=optional
     */
    public function details(Request $request, $year, $month)
    {
        $user = $request->user();

        if (!$user || !in_array($user->role, ['superadmin', 'owner'])) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $request->validate([
            'sales_id' => ['nullable', 'integer', 'exists:users,id'],
            'owner_id' => ['nullable', 'integer', 'exists:users,id']
        ]);

        $salesId = $request->input('sales_id');
        $ownerId = $request->input('owner_id');

        // Determine scope
        if ($user->role === 'owner') {
            $ownerId = $user->id;
        } elseif ($user->role === 'superadmin' && !$ownerId) {
            return response()->json(['message' => 'owner_id required for superadmin'], 400);
        }

        // Get transaksi kredit pada bulan tersebut
        $creditTransactions = $this->getCreditTransactionsForMonth($ownerId, $salesId, $year, $month);

        // Get pembayaran yang dialokasikan ke hutang bulan tersebut
        $payments = $this->getPaymentsForMonth($ownerId, $year, $month);

        // Get ringkasan per mitra
        $mitrasSummary = $this->getMitrasSummaryForMonth($ownerId, $salesId, $year, $month);

        return response()->json([
            'year' => $year,
            'month' => $month,
            'month_name' => $this->getMonthName($month),
            'credit_transactions' => $creditTransactions,
            'payments' => $payments,
            'mitras_summary' => $mitrasSummary
        ]);
    }

    /**
     * Kirim bonus untuk periode tertentu
     * POST /api/sales-bonus/{year}/{month}/send
     */
    public function send(Request $request, $year, $month)
    {
        $user = $request->user();

        if (!$user || !in_array($user->role, ['superadmin', 'owner'])) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $data = $request->validate([
            'sales_id' => ['required', 'integer', 'exists:users,id'],
            'bonus_description' => ['required', 'string', 'max:1000'],
            'bonus_amount' => ['nullable', 'integer', 'min:0'],
            'owner_id' => ['nullable', 'integer', 'exists:users,id'] // untuk superadmin
        ]);

        $salesId = $data['sales_id'];
        $ownerId = $data['owner_id'] ?? null;

        // Determine scope
        if ($user->role === 'owner') {
            $ownerId = $user->id;
        } elseif ($user->role === 'superadmin' && !$ownerId) {
            return response()->json(['message' => 'owner_id required for superadmin'], 400);
        }

        // Validasi sales belongs to owner
        $sales = User::find($salesId);
        if (!$sales || $sales->role !== 'sales' || $sales->owner_id !== $ownerId) {
            return response()->json(['message' => 'Invalid sales for this owner'], 400);
        }

        // Check apakah sudah pernah dikirim
        if (SalesBonusPayout::alreadySent($ownerId, $salesId, $year, $month)) {
            return response()->json(['message' => 'Bonus untuk periode ini sudah pernah dikirim'], 400);
        }

        // Calculate data bulan tersebut
        $monthData = $this->calculateMonthData($ownerId, $salesId, $year, $month);

        // Validasi eligibility
        if (!$monthData['eligible_to_send']) {
            return response()->json([
                'message' => 'Bonus belum eligible untuk dikirim',
                'reason' => $monthData['eligibility_reason']
            ], 400);
        }

        // Create bonus payout record
        $payout = SalesBonusPayout::create([
            'owner_id' => $ownerId,
            'sales_id' => $salesId,
            'year' => $year,
            'month' => $month,
            'debt_total' => $monthData['debt_total'],
            'paid_total' => $monthData['paid_total'],
            'percent_paid' => $monthData['percent_paid'],
            'bonus_description' => $data['bonus_description'],
            'bonus_amount' => $data['bonus_amount'] ?? null,
            'status' => 'sent',
            'created_by' => $user->id
        ]);

        return response()->json($payout->load(['owner:id,name', 'sales:id,name']), 201);
    }

    /**
     * Get riwayat bonus untuk sales
     * GET /api/sales-bonus/history
     */
    public function history(Request $request)
    {
        $user = $request->user();

        if (!$user) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        $perPage = (int) $request->query('per_page', 20);
        $query = SalesBonusPayout::with(['owner:id,name', 'sales:id,name']);

        // Scope berdasarkan role
        if ($user->role === 'sales') {
            $query->where('sales_id', $user->id);
        } elseif ($user->role === 'owner') {
            $query->where('owner_id', $user->id);
        } elseif ($user->role === 'superadmin') {
            // Superadmin bisa melihat semua
        } else {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        $payouts = $query->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->paginate($perPage);

        return response()->json($payouts);
    }

    /**
     * Calculate data untuk bulan tertentu
     */
    private function calculateMonthData($ownerId, $salesId, $year, $month)
    {
        // Total hutang yang tercipta pada bulan tersebut
        $debtTotal = $this->getDebtTotalForMonth($ownerId, $salesId, $year, $month);

        // Total yang sudah dibayar dari hutang bulan tersebut (cohort-based)
        $paidTotal = $this->getPaidTotalForMonth($ownerId, $salesId, $year, $month);

        // Persentase
        $percentPaid = $debtTotal > 0 ? ($paidTotal / $debtTotal) * 100 : 0;

        // Check eligibility
        $eligibleToSend = SalesBonusPayout::isEligibleToSend($year, $month, $percentPaid)
            && !SalesBonusPayout::alreadySent($ownerId, $salesId, $year, $month);

        $eligibilityReason = '';
        if ($percentPaid <= 80) {
            $eligibilityReason = 'Persentase pembayaran masih ' . number_format($percentPaid, 1) . '% (harus >80%)';
        } elseif (!SalesBonusPayout::isEligibleToSend($year, $month, 100)) {
            $eligibilityReason = 'Belum melewati 3 bulan dari periode tersebut';
        } elseif (SalesBonusPayout::alreadySent($ownerId, $salesId, $year, $month)) {
            $eligibilityReason = 'Bonus sudah pernah dikirim';
        }

        return [
            'month' => $month,
            'month_name' => $this->getMonthName($month),
            'debt_total' => $debtTotal,
            'paid_total' => $paidTotal,
            'percent_paid' => round($percentPaid, 2),
            'eligible_to_send' => $eligibleToSend,
            'eligibility_reason' => $eligibilityReason,
            'already_sent' => SalesBonusPayout::alreadySent($ownerId, $salesId, $year, $month)
        ];
    }

    /**
     * Get total hutang yang tercipta pada bulan tertentu
     */
    private function getDebtTotalForMonth($ownerId, $salesId, $year, $month)
    {
        $query = Transaction::where('payment_method', 'credit')
            ->whereIn('status', ['approved', 'shipped', 'delivered'])
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month);

        if ($salesId) {
            $query->where('sales_id', $salesId);
        }

        // Scope by owner
        if ($ownerId) {
            $query->whereHas('sales', function ($q) use ($ownerId) {
                $q->where('owner_id', $ownerId)->orWhere('id', $ownerId);
            });
        }

        return $query->sum('total') ?? 0;
    }

    /**
     * Get total yang sudah dibayar dari hutang bulan tertentu (cohort-based dengan FIFO)
     */
    private function getPaidTotalForMonth($ownerId, $salesId, $year, $month)
    {
        // Ambil semua transaksi kredit pada bulan tersebut
        $creditTransactions = Transaction::where('payment_method', 'credit')
            ->whereIn('status', ['approved', 'shipped', 'delivered'])
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->when($salesId, function ($q) use ($salesId) {
                $q->where('sales_id', $salesId);
            })
            ->when($ownerId, function ($q) use ($ownerId) {
                $q->whereHas('sales', function ($sq) use ($ownerId) {
                    $sq->where('owner_id', $ownerId);
                });
            })
            ->with('mitra')
            ->orderBy('created_at')
            ->get();

        $totalPaid = 0;

        foreach ($creditTransactions as $transaction) {
            // Ambil pembayaran untuk mitra ini setelah tanggal transaksi
            $payments = ReceivablePayment::where('mitra_id', $transaction->mitra_id)
                ->where('paid_at', '>', $transaction->created_at)
                ->when($ownerId, function ($q) use ($ownerId) {
                    $q->whereHas('mitra', function ($sq) use ($ownerId) {
                        $sq->where('owner_id', $ownerId);
                    });
                })
                ->orderBy('paid_at')
                ->get();

            // Alokasi FIFO: pembayaran dialokasikan ke transaksi tertua dulu
            $remainingDebt = $transaction->total;
            foreach ($payments as $payment) {
                if ($remainingDebt <= 0) break;

                $allocatedAmount = min($payment->amount, $remainingDebt);
                $totalPaid += $allocatedAmount;
                $remainingDebt -= $allocatedAmount;
            }
        }

        return $totalPaid;
    }

    /**
     * Get transaksi kredit untuk bulan tertentu
     */
    private function getCreditTransactionsForMonth($ownerId, $salesId, $year, $month)
    {
        return Transaction::with(['mitra:id,name', 'sales:id,name'])
            ->where('payment_method', 'credit')
            ->whereIn('status', ['approved', 'shipped', 'delivered'])
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->when($salesId, function ($q) use ($salesId) {
                $q->where('sales_id', $salesId);
            })
            ->when($ownerId, function ($q) use ($ownerId) {
                $q->whereHas('sales', function ($sq) use ($ownerId) {
                    $sq->where('owner_id', $ownerId);
                });
            })
            ->orderBy('created_at')
            ->get()
            ->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'date' => $transaction->created_at->format('Y-m-d'),
                    'mitra' => $transaction->mitra->name ?? '-',
                    'sales' => $transaction->sales->name ?? '-',
                    'total' => $transaction->total,
                    'status' => $transaction->status
                ];
            });
    }

    /**
     * Get pembayaran yang dialokasikan ke hutang bulan tertentu
     */
    private function getPaymentsForMonth($ownerId, $year, $month)
    {
        $periodStart = Carbon::create($year, $month, 1);
        $periodEnd = $periodStart->copy()->endOfMonth();

        return ReceivablePayment::with(['mitra:id,name'])
            ->where('paid_at', '>', $periodEnd) // Pembayaran setelah periode hutang
            ->when($ownerId, function ($q) use ($ownerId) {
                $q->whereHas('mitra', function ($sq) use ($ownerId) {
                    $sq->where('owner_id', $ownerId);
                });
            })
            ->orderBy('paid_at')
            ->get()
            ->map(function ($payment) {
                return [
                    'id' => $payment->id,
                    'date' => $payment->paid_at->format('Y-m-d'),
                    'mitra' => $payment->mitra->name ?? '-',
                    'amount' => $payment->amount,
                    'note' => $payment->note
                ];
            });
    }

    /**
     * Get ringkasan per mitra untuk bulan tertentu
     */
    private function getMitrasSummaryForMonth($ownerId, $salesId, $year, $month)
    {
        $creditTransactions = Transaction::where('payment_method', 'credit')
            ->whereIn('status', ['approved', 'shipped', 'delivered'])
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->when($salesId, function ($q) use ($salesId) {
                $q->where('sales_id', $salesId);
            })
            ->when($ownerId, function ($q) use ($ownerId) {
                $q->whereHas('sales', function ($sq) use ($ownerId) {
                    $sq->where('owner_id', $ownerId);
                });
            })
            ->with('mitra')
            ->get()
            ->groupBy('mitra_id');

        $summary = [];
        foreach ($creditTransactions as $mitraId => $transactions) {
            $mitra = $transactions->first()->mitra;
            $totalDebt = $transactions->sum('total');

            // Hitung pembayaran untuk mitra ini dari hutang periode ini
            $totalPaid = 0;
            $periodEnd = Carbon::create($year, $month, 1)->endOfMonth();

            $payments = ReceivablePayment::where('mitra_id', $mitraId)
                ->where('paid_at', '>', $periodEnd)
                ->sum('amount');

            $totalPaid = min($payments, $totalDebt); // Tidak boleh lebih dari total hutang
            $percentPaid = $totalDebt > 0 ? ($totalPaid / $totalDebt) * 100 : 0;

            $summary[] = [
                'mitra_id' => $mitraId,
                'mitra_name' => $mitra->name,
                'debt_total' => $totalDebt,
                'paid_total' => $totalPaid,
                'percent_paid' => round($percentPaid, 2),
                'transactions_count' => $transactions->count()
            ];
        }

        return collect($summary)->sortByDesc('debt_total')->values()->all();
    }

    /**
     * Get nama bulan
     */
    private function getMonthName($month)
    {
        $monthNames = [
            1 => 'Januari',
            2 => 'Februari',
            3 => 'Maret',
            4 => 'April',
            5 => 'Mei',
            6 => 'Juni',
            7 => 'Juli',
            8 => 'Agustus',
            9 => 'September',
            10 => 'Oktober',
            11 => 'November',
            12 => 'Desember'
        ];

        return $monthNames[$month] ?? 'Unknown';
    }
}

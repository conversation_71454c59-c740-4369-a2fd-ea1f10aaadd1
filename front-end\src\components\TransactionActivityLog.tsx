import React, { useState, useEffect } from 'react';
import { TransactionLog } from '../types';
import { apiFetch } from '../services/api';
import { Clock, User, CheckCircle, XCircle, Truck, Package } from 'lucide-react';

interface TransactionActivityLogProps {
  transactionId: string;
}

const TransactionActivityLog: React.FC<TransactionActivityLogProps> = ({ transactionId }) => {
  const [logs, setLogs] = useState<TransactionLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchLogs = async () => {
      try {
        setLoading(true);
        const response = await apiFetch(`/api/transaction-logs/transaction/${transactionId}`);

        // Normalize various API response shapes to an array of logs
        const raw = response as any;
        const logsData = Array.isArray(raw)
          ? raw
          : Array.isArray(raw?.data)
            ? raw.data
            : Array.isArray(raw?.logs)
              ? raw.logs
              : Array.isArray(raw?.data?.logs)
                ? raw.data.logs
                : [];

        setLogs(logsData.map((log: any) => ({
          id: String(log.id),
          transactionId: String(log.transaction_id ?? log.transactionId),
          userId: String(log.user_id ?? log.userId),
          action: log.action,
          previousStatus: log.previous_status ?? log.previousStatus,
          newStatus: log.new_status ?? log.newStatus,
          notes: log.notes,
          metadata: log.metadata || {},
          createdAt: log.created_at ?? log.createdAt,
          user: {
            id: String(log.user?.id || ''),
            name: log.user?.name || 'Unknown User',
            role: log.user?.role || 'unknown',
            email: log.user?.email
          }
        })));
      } catch (err: any) {
        console.error('Error fetching transaction logs:', err);
        setError(err.message || 'Gagal memuat riwayat aktivitas');
      } finally {
        setLoading(false);
      }
    };

    if (transactionId) {
      fetchLogs();
    }
  }, [transactionId]);

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'approve_gudang':
        return <CheckCircle size={20} className="text-green-600" />;
      case 'reject':
        return <XCircle size={20} className="text-red-600" />;
      case 'ship':
        return <Truck size={20} className="text-blue-600" />;
      case 'deliver':
        return <Package size={20} className="text-purple-600" />;
      default:
        return <Clock size={20} className="text-gray-600" />;
    }
  };

  const getActionLabel = (action: string) => {
    const labels = {
      'approve_gudang': 'Menyetujui Transaksi',
      'reject': 'Menolak Transaksi',
      'ship': 'Mengirim Barang',
      'deliver': 'Menyelesaikan Pengiriman'
    };
    return labels[action as keyof typeof labels] || action;
  };

  const getStatusLabel = (status: string) => {
    const labels = {
      'pending_owner': 'Menunggu Persetujuan Owner',
      'pending_gudang': 'Menunggu Persetujuan Gudang',
      'approved': 'Disetujui',
      'rejected': 'Ditolak',
      'shipped': 'Dikirim',
      'delivered': 'Selesai'
    };
    return labels[status as keyof typeof labels] || status;
  };

  const getRoleLabel = (role: string) => {
    const labels = {
      'superadmin': 'Super Admin',
      'owner': 'Owner',
      'admin_gudang': 'Admin Gudang',
      'sales': 'Sales'
    };
    return labels[role as keyof typeof labels] || role;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
        <span className="ml-2 text-gray-600">Memuat riwayat aktivitas...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <XCircle size={48} className="mx-auto text-red-400 mb-4" />
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  if (logs.length === 0) {
    return (
      <div className="text-center py-8">
        <Clock size={48} className="mx-auto text-gray-400 mb-4" />
        <p className="text-gray-600">Belum ada aktivitas untuk transaksi ini</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h4 className="text-lg font-semibold text-gray-900 mb-4">Riwayat Aktivitas</h4>

      <div className="flow-root">
        <ul className="-mb-8">
          {logs.map((log, index) => (
            <li key={log.id}>
              <div className="relative pb-8">
                {index !== logs.length - 1 && (
                  <span
                    className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                    aria-hidden="true"
                  />
                )}
                <div className="relative flex space-x-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-white border-2 border-gray-300">
                    {getActionIcon(log.action)}
                  </div>
                  <div className="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {getActionLabel(log.action)}
                      </p>
                      <p className="text-sm text-gray-600">
                        Status: {getStatusLabel(log.previousStatus)} → {getStatusLabel(log.newStatus)}
                      </p>
                      {log.notes && (
                        <p className="text-sm text-gray-500 mt-1">{log.notes}</p>
                      )}
                      <div className="flex items-center mt-2 space-x-4">
                        <div className="flex items-center text-xs text-gray-500">
                          <User size={14} className="mr-1" />
                          <span className="font-medium">{log.user.name}</span>
                          <span className="ml-1">({getRoleLabel(log.user.role)})</span>
                        </div>
                      </div>
                    </div>
                    <div className="whitespace-nowrap text-right text-sm text-gray-500">
                      <time dateTime={log.createdAt}>
                        {formatDate(log.createdAt)}
                      </time>
                    </div>
                  </div>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default TransactionActivityLog;

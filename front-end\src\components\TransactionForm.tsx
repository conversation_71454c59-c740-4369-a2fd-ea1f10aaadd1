import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
// import { mockMitras, mockProducts } from '../services/mockData';
import { apiFetch } from '../services/api';
import { Mitra, Product, TransactionItem, ProductBonusReward } from '../types';
import { sendWhatsAppMessage } from '../services/whatsappService';
import { Plus, Minus, Trash2, Check } from 'lucide-react';

// Simple Toast
const Toast: React.FC<{ type: 'success' | 'error'; message: string }> = ({ type, message }) => (
  <div className={`fixed top-6 right-6 z-50 px-4 py-3 rounded-lg shadow-lg text-sm ${type === 'success' ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}`}>
    {message}
  </div>
);

const TransactionForm: React.FC = () => {
  const { user } = useAuth();
  const [selectedMitra, setSelectedMitra] = useState<Mitra | null>(null);
  const [items, setItems] = useState<TransactionItem[]>([]);
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'credit'>('cash');
  const [requestDiscount, setRequestDiscount] = useState(false);
  const [toast, setToast] = useState<{ type: 'success' | 'error'; message: string } | null>(null);

  const [discountType, setDiscountType] = useState<'total' | 'per_item'>('total');
  const [discountValue, setDiscountValue] = useState(0);
  const [discountReason, setDiscountReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [mitras, setMitras] = useState<Mitra[]>([]);
  const [products, setProducts] = useState<Product[]>([]);

  useEffect(() => {
    (async () => {
      try {
        const [mRes, pRes] = await Promise.all([
          apiFetch('/api/mitras'),
          apiFetch('/api/products'),
        ]);
        const mitraItems: any[] = mRes.data ?? mRes;
        setMitras(mitraItems.map(m => ({
          id: String(m.id), name: m.name, phone: m.phone || '', address: m.address || '',
          creditLimit: Number(m.credit_limit || 0), currentDebt: Number(m.current_debt || 0),
          status: m.status === 'inactive' ? 'inactive' : 'active', createdBy: String(m.created_by ?? ''),
          createdAt: m.created_at ?? new Date().toISOString()
        })));
        const prodItems: any[] = pRes.data ?? pRes;
        setProducts(prodItems.map((p: any) => ({
          id: String(p.id), name: p.name, categoryId: String(p.category_id ?? ''),
          category: p.category?.name ?? '', price: +p.price, stock: +p.stock,
          unit: p.unit ?? '', unit_id: p.unit_id, unit_legacy: p.unit_legacy,
          description: p.description ?? '',
          createdBy: String(p.created_by ?? ''), createdAt: p.created_at ?? new Date().toISOString()
        })));
      } catch (e) { console.error(e); }
    })();
  }, []);
  // Normalize discounts when switching types to avoid stale negative totals
  useEffect(() => {
    if (discountType === 'total') {
      // per-item discounts still apply to item totals; ensure non-negative
      setItems(prev => prev.map(it => ({
        ...it,
        discount: Math.max(0, it.discount),
        total: Math.max(0, (it.price * it.quantity) - Math.max(0, it.discount)),
      })));
    } else {


      // Switching to per_item: clamp total discount value but do not apply to item totals
      setDiscountValue(v => Math.max(0, v));
      setItems(prev => prev.map(it => ({
        ...it,
        discount: Math.max(0, it.discount),
        total: Math.max(0, (it.price * it.quantity) - Math.max(0, it.discount)),
      })));
    }
  }, [discountType]);


  const [success, setSuccess] = useState(false);

  // Compute per-item total based on current discount mode
  const computeItemTotal = React.useCallback((item: TransactionItem) => {
    const base = Number(item.price) * Number(item.quantity);
    // In per_item mode, discount value is per 1 quantity; total discount = discount * qty
    const perItemUnitDisc = (requestDiscount && discountType === 'per_item') ? Math.max(0, Number(item.discount) || 0) : 0;
    const total = base - (perItemUnitDisc * Number(item.quantity));
    return total > 0 ? total : 0;
  }, [requestDiscount, discountType]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const addItem = () => {
    setItems([...items, {
      productId: '',
      productName: '',
      price: 0,
      quantity: 1,
      discount: 0,
      total: 0
    }]);
  };

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const updateItem = async (index: number, field: keyof TransactionItem, value: any) => {
    const newItems = [...items];

    // Sanitize discount to be non-negative
    if (field === 'discount') {
      value = Math.max(0, parseInt(String(value)) || 0);
    }

    newItems[index] = { ...newItems[index], [field]: value };

    if (field === 'productId') {
      const product = products.find(p => p.id === value);
      if (product) {
        newItems[index].productName = product.name;
        newItems[index].price = product.price;

        // Fetch bonus/reward information for the selected product
        if (value) {
          try {
            const bonusRewardData: ProductBonusReward = await apiFetch(`/api/products/${value}/bonus-reward`);
            newItems[index].bonusRewardInfo = bonusRewardData;
          } catch (error) {
            console.error('Error fetching bonus/reward info:', error);
            newItems[index].bonusRewardInfo = undefined;
          }
        } else {
          newItems[index].bonusRewardInfo = undefined;
        }
      }
    }

    // Recalculate item.total always from computeItemTotal
    const item = newItems[index];
    item.total = computeItemTotal(item);

    setItems(newItems);
  };

  const subtotal = items.reduce((sum, item) => sum + computeItemTotal(item), 0);
  const totalAfterDiscount = requestDiscount && discountType === 'total'
    ? Math.max(0, subtotal - Math.max(0, discountValue))
    : subtotal;
  const apiItems = items.map(i => ({ product_id: i.productId, product_name: i.productName, price: i.price, quantity: i.quantity, discount: (requestDiscount && discountType === 'per_item') ? i.discount : 0, total: computeItemTotal(i) }));
  const isTotalDiscountInvalid = requestDiscount && discountType === 'total' && discountValue > subtotal;


  const getUnitName = (product: Product) => {
    if (typeof product.unit === 'object' && product.unit) {
      return product.unit.name;
    }
    if (product.unit_id) {
      // Note: We don't have units array here, so fallback to unit object or legacy
      return '';
    }
    return product.unit_legacy || product.unit || '';
  };

  const validateTransaction = (): string[] => {
    const errors: string[] = [];

    if (!selectedMitra) {
      errors.push('Pilih mitra terlebih dahulu');
    }

    if (items.length === 0) {
      errors.push('Tambahkan minimal satu item');
    }

    items.forEach((item, index) => {
      if (!item.productId) {
        errors.push(`Item ${index + 1}: Pilih produk`);
        return;
      }
      if (item.quantity <= 0) {
        errors.push(`Item ${index + 1}: Quantity harus lebih dari 0`);
      }
      const prod = products.find(p => p.id === item.productId);
      if (prod && item.quantity > prod.stock) {
        errors.push(`Item ${index + 1}: Quantity (${item.quantity}) melebihi stok tersedia (${prod.stock} ${getUnitName(prod)})`);
      }
    });

    if (paymentMethod === 'credit' && selectedMitra) {
      const availableCredit = selectedMitra.creditLimit - selectedMitra.currentDebt;
      if (totalAfterDiscount > availableCredit) {
        errors.push(`Transaksi melebihi limit kredit mitra. Tersedia: ${formatCurrency(availableCredit)}`);
      }

    }

    if (requestDiscount) {
      if (!discountReason.trim()) {
        errors.push('Alasan diskon wajib diisi');
      }
      if (discountType === 'total') {
        if (discountValue <= 0) {
          errors.push('Nilai diskon total harus lebih dari 0');
        }
        if (discountValue > subtotal) {
          errors.push('Nilai diskon total melebihi subtotal');
        }
      } else {
        // per item: pastikan tidak ada discount negatif dan tidak melebihi total item
        items.forEach((item, index) => {
          if (item.discount < 0) {
            errors.push(`Item ${index + 1}: Diskon tidak boleh negatif`);
          }
          const maxDisc = item.price * item.quantity;
          if (item.discount > maxDisc) {
            errors.push(`Item ${index + 1}: Diskon melebihi total item`);
          }
        });
      }
    }

    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const errors = validateTransaction();
    if (errors.length > 0) {
      setToast({ type: 'error', message: errors.join(' • ') });
      setTimeout(() => setToast(null), 3000);
      return;
    }

    // Hard guard: jangan kirim jika diskon total > subtotal
    if (requestDiscount && discountType === 'total' && discountValue > subtotal) {
      setToast({ type: 'error', message: 'Nilai diskon total melebihi subtotal' });
      setTimeout(() => setToast(null), 3000);
      return;
    }

    setIsSubmitting(true);

    try {
      // Normalisasi nilai diskon untuk payload
      const payloadDiscountType = requestDiscount ? discountType : 'none';
      const payloadDiscountValue = requestDiscount ? (discountType === 'total' ? Math.min(discountValue, subtotal) : 0) : 0;

      // Create transaction via API
      const created = await apiFetch('/api/transactions', {
        method: 'POST',
        body: JSON.stringify({
          mitra_id: selectedMitra!.id,
          sales_id: user!.id,
          items: apiItems,
          subtotal,
          discount_type: payloadDiscountType,
          discount_value: payloadDiscountValue,
          discount_reason: requestDiscount ? discountReason : '',
          total: totalAfterDiscount,
          payment_method: paymentMethod,
          // status akan ditentukan oleh backend sesuai kebijakan (first credit auto gudang; otherwise owner)
          status: requestDiscount ? 'pending_owner' : 'pending_gudang',
          // Kirim format yang lebih aman untuk MySQL
          due_date: paymentMethod === 'credit'
            ? new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')
            : null,
        })
      });

      const transactionId = created.id || `TXN-${Date.now()}`;

      // Send WhatsApp notification sesuai status dari backend
      const recipients = await apiFetch(`/api/transactions/${transactionId}/recipients`).catch(() => null);
      const createdStatus = String(created.status || '').toLowerCase();
      if (createdStatus === 'pending_owner') {
        // Owner approval required -> notify owner + mitra
        if (recipients?.owner) {
          await sendWhatsAppMessage({
            receiptnumber: recipients.owner,
            message: `Pengajuan transaksi membutuhkan persetujuan Owner. Transaksi ${transactionId} dari sales ${user?.name}. Mitra: ${selectedMitra?.name}${requestDiscount ? `, Pengajuan diskon: ${formatCurrency(discountType === 'per_item' ? items.reduce((sum, it) => sum + ((it.discount || 0) * (it.quantity || 0)), 0) : discountValue)}` : ''}.`
          });
        }
        if (recipients?.mitra) {
          await sendWhatsAppMessage({
            receiptnumber: recipients.mitra,
            message: `Transaksi ${transactionId} sedang menunggu persetujuan Owner.`
          });
        }
      } else {
        // Masuk ke gudang -> notify mitra + admin gudang
        if (recipients?.mitra) {
          await sendWhatsAppMessage({
            receiptnumber: recipients.mitra,
            message: `Transaksi baru ${transactionId} dibuat. Total: ${formatCurrency(totalAfterDiscount)}. Admin Gudang akan memproses pesanan Anda.`
          });
        }
        if (Array.isArray(recipients?.admin_gudang)) {
          for (const g of recipients.admin_gudang) {
            await sendWhatsAppMessage({
              receiptnumber: g,
              message: `Transaksi baru ${transactionId} siap untuk diproses. Mitra: ${selectedMitra?.name}, Total: ${formatCurrency(totalAfterDiscount)}.`
            });
          }
        }
      }

      setSuccess(true);

      // Reset form after success
      setTimeout(() => {
        setSelectedMitra(null);
        setItems([]);
        setPaymentMethod('cash');
        setRequestDiscount(false);
        setDiscountType('total');
        setDiscountValue(0);
        setDiscountReason('');
        setSuccess(false);
      }, 3000);

    } catch (error) {
      console.error('Error creating transaction:', error);
      setToast({ type: 'error', message: 'Gagal membuat transaksi. Silakan coba lagi.' });
      setTimeout(() => setToast(null), 3000);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (success) {
    return (
      <div className="max-w-2xl mx-auto">
        {toast && <Toast type={toast.type} message={toast.message} />}
        <div className="bg-green-50 border border-green-200 rounded-xl p-8 text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Check size={32} className="text-green-600" />
          </div>
          <h2 className="text-2xl font-bold text-green-900 mb-2">
            Transaksi Berhasil Dibuat!
          </h2>
          <p className="text-green-700 mb-4">
            Request transaksi telah dikirim untuk approval.
            {requestDiscount ? ' Owner akan mereview pengajuan diskon terlebih dahulu.' : ' Admin gudang akan memproses transaksi ini.'}
          </p>
          <p className="text-sm text-green-600">
            Notifikasi WhatsApp telah dikirim. Form akan direset otomatis...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {toast && <Toast type={toast.type} message={toast.message} />}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Request Transaksi Baru</h2>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Mitra Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Pilih Mitra
            </label>
            <select
              value={selectedMitra?.id || ''}
              onChange={(e) => {
                const mitra = mitras.find(m => m.id === e.target.value);
                setSelectedMitra(mitra || null);
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
              required
            >
              <option value="">-- Pilih Mitra --</option>
              {mitras.map((mitra) => (
                <option key={mitra.id} value={mitra.id}>
                  {mitra.name} - {mitra.phone}
                </option>
              ))}
            </select>

            {selectedMitra && (
              <div className="mt-2 p-3 bg-blue-50 rounded-lg">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Limit Kredit:</span> {formatCurrency(selectedMitra.creditLimit)}
                  </div>
                  <div>
                    <span className="font-medium">Hutang Saat Ini:</span> {formatCurrency(selectedMitra.currentDebt)}
                  </div>
                  <div className="col-span-2">
                    <span className="font-medium">Kredit Tersedia:</span>
                    <span className={selectedMitra.currentDebt > 0 ? 'text-red-600' : 'text-green-600'}>
                      {formatCurrency(selectedMitra.creditLimit - selectedMitra.currentDebt)}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Items */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Items Pesanan
              </label>
              <button
                type="button"
                onClick={addItem}
                className="flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Plus size={16} className="mr-1" />
                Tambah Item
              </button>
            </div>

            <div className="space-y-3">
              {items.map((item, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-start">
                    <div className="md:col-span-2">
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Produk
                      </label>
                      <select
                        value={item.productId}
                        onChange={(e) => updateItem(index, 'productId', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded text-sm focus:ring-green-500 focus:border-green-500"
                        required
                      >
                        <option value="">-- Pilih Produk --</option>
                        {products.map((product) => (
                          <option key={product.id} value={product.id}>
                            {product.name} - {formatCurrency(product.price)}
                          </option>
                        ))}
                      </select>


                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Quantity
                      </label>
                      <div className="flex items-center">
                        <button
                          type="button"
                          onClick={() => updateItem(index, 'quantity', Math.max(1, item.quantity - 1))}
                          className="p-1 border border-gray-300 rounded-l hover:bg-gray-100"
                        >
                          <Minus size={16} />
                        </button>
                        <input
                          type="number"
                          value={item.quantity}
                          onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value) || 1)}
                          className="w-full px-2 py-2 border-t border-b border-gray-300 text-center text-sm focus:ring-green-500 focus:border-green-500"
                          min="1"
                        />
                        <button
                          type="button"
                          onClick={() => updateItem(index, 'quantity', item.quantity + 1)}
                          className="p-1 border border-gray-300 rounded-r hover:bg-gray-100"
                        >
                          <Plus size={16} />
                        </button>
                      </div>
                    </div>

                    {discountType === 'per_item' && requestDiscount && (
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Diskon Item
                        </label>
                        <input
                          type="number"
                          value={item.discount}
                          onChange={(e) => updateItem(index, 'discount', Math.max(0, parseInt(e.target.value) || 0))}
                          onBlur={(e) => updateItem(index, 'discount', Math.max(0, parseInt(e.currentTarget.value) || 0))}
                          className="w-full px-3 py-2 border border-gray-300 rounded text-sm focus:ring-green-500 focus:border-green-500"
                          min="0"
                        />
                      </div>
                    )}

                    <div className="flex items-start justify-between">
                      <div>
                        <span className="block text-xs font-medium text-gray-700 mb-1">Total</span>
                        <span className="text-sm font-medium">{formatCurrency(computeItemTotal(item))}</span>
                      </div>
                      <button
                        type="button"
                        onClick={() => removeItem(index)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>

                  {/* Bonus/Reward Alert Info - full width under the item row */}
                  {item.bonusRewardInfo && (item.bonusRewardInfo.has_bonus || item.bonusRewardInfo.has_reward) && (
                    <div className="col-span-1 md:col-span-5 mt-2">
                      <div className="w-full rounded-md border border-blue-200 bg-blue-50 p-3">
                        <div className="flex flex-wrap items-center gap-2 mb-2">
                          <span className="text-sm font-semibold text-blue-900">Promo tersedia untuk produk ini</span>
                          {item.bonusRewardInfo.has_bonus && (
                            <span className="inline-flex items-center rounded-full bg-blue-100 text-blue-800 px-2 py-0.5 text-[11px] font-medium">Bonus</span>
                          )}
                          {item.bonusRewardInfo.has_reward && (
                            <span className="inline-flex items-center rounded-full bg-blue-100 text-blue-800 px-2 py-0.5 text-[11px] font-medium">Reward</span>
                          )}
                        </div>

                        {item.bonusRewardInfo.has_bonus && (
                          <div className="mb-2">
                            <div className="text-xs font-semibold text-blue-800 mb-1">Bonus</div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                              {item.bonusRewardInfo.bonuses.map((bonus, bonusIndex) => (
                                <div key={bonusIndex} className="text-xs text-blue-800 bg-white border border-blue-100 rounded-md p-2">
                                  <div className="flex flex-wrap items-center gap-2">
                                    <span className="font-medium">{bonus.name}</span>
                                    <span className="inline-flex items-center rounded-full bg-blue-100 text-blue-800 px-2 py-0.5 text-[11px] font-medium">{bonus.type === 'periode' ? 'Periode' : 'Kuota'}</span>
                                    {bonus.minimum_quantity > 1 && (
                                      <span className="inline-flex items-center rounded-full bg-blue-100 text-blue-800 px-2 py-0.5 text-[11px] font-medium">Min {bonus.minimum_quantity} unit</span>
                                    )}
                                    {bonus.product && (
                                      <span className="inline-flex items-center rounded-full bg-blue-100 text-blue-800 px-2 py-0.5 text-[11px] font-medium">Produk: {bonus.product}</span>
                                    )}
                                    {!bonus.product && bonus.category && (
                                      <span className="inline-flex items-center rounded-full bg-blue-100 text-blue-800 px-2 py-0.5 text-[11px] font-medium">Kategori: {bonus.category}</span>
                                    )}
                                  </div>
                                  <div className="mt-1 text-blue-700">{bonus.bonus_description}</div>
                                  {bonus.minimum_quantity > 1 && item.quantity < bonus.minimum_quantity && (
                                    <div className="mt-1 text-[11px] text-blue-600">Tambahkan {Math.max(0, bonus.minimum_quantity - Number(item.quantity))} lagi untuk memenuhi syarat bonus.</div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {item.bonusRewardInfo.has_reward && (
                          <div>
                            <div className="text-xs font-semibold text-blue-800 mb-1">Reward</div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                              {item.bonusRewardInfo.rewards.map((reward, rewardIndex) => (
                                <div key={rewardIndex} className="text-xs text-blue-800 bg-white border border-blue-100 rounded-md p-2">
                                  <div className="flex flex-wrap items-center gap-2">
                                    <span className="font-medium">{reward.name}</span>
                                    {reward.target_quantity ? (
                                      <span className="inline-flex items-center rounded-full bg-blue-100 text-blue-800 px-2 py-0.5 text-[11px] font-medium">Target {reward.target_quantity} unit</span>
                                    ) : null}
                                    {reward.product && (
                                      <span className="inline-flex items-center rounded-full bg-blue-100 text-blue-800 px-2 py-0.5 text-[11px] font-medium">Produk: {reward.product}</span>
                                    )}
                                    {!reward.product && reward.category && (
                                      <span className="inline-flex items-center rounded-full bg-blue-100 text-blue-800 px-2 py-0.5 text-[11px] font-medium">Kategori: {reward.category}</span>
                                    )}
                                  </div>
                                  <div className="mt-1 text-blue-700">{reward.reward_description || reward.description}</div>
                                  {reward.target_quantity && Number(item.quantity) < Number(reward.target_quantity) && (
                                    <div className="mt-1 text-[11px] text-blue-600">Tambah {Math.max(0, Number(reward.target_quantity) - Number(item.quantity))} lagi untuk mencapai target reward.</div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                </div>
              ))}
            </div>

            {items.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                Belum ada item ditambahkan
              </div>
            )}
          </div>

          {/* Payment Method */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Metode Pembayaran
            </label>
            <div className="grid grid-cols-2 gap-4">
              <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  value="cash"
                  checked={paymentMethod === 'cash'}
                  onChange={(e) => setPaymentMethod(e.target.value as 'cash' | 'credit')}
                  className="mr-3 text-green-600"
                />
                <span className="font-medium">Bayar Lunas</span>



              </label>
              <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  value="credit"
                  checked={paymentMethod === 'credit'}
                  onChange={(e) => setPaymentMethod(e.target.value as 'cash' | 'credit')}
                  className="mr-3 text-green-600"
                />
                <div>
                  <span className="font-medium">Hutang</span>
                  <p className="text-xs text-gray-500">Tenor max 3 bulan</p>
                </div>
              </label>
            </div>

          </div>

          {/* Discount Request */}
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={requestDiscount}
                onChange={(e) => setRequestDiscount(e.target.checked)}
                className="mr-2 text-green-600"
              />
              <span className="text-sm font-medium text-gray-700">Request Pengajuan Diskon</span>
            </label>

            {requestDiscount && (
              <div className="mt-4 p-4 border border-orange-200 rounded-lg bg-orange-50">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tipe Diskon
                    </label>
                    <select
                      value={discountType}
                      onChange={(e) => setDiscountType(e.target.value as 'total' | 'per_item')}
                      className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-green-500 focus:border-green-500"
                    >
                      <option value="total">Diskon Total Keseluruhan</option>
                      <option value="per_item">Diskon Per Item Produk</option>
                    </select>
                  </div>

                  {discountType === 'total' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Nilai Diskon Total
                      </label>
                      <input
                        type="number"
                        value={discountValue}
                        onChange={(e) => {
                          const v = parseInt(e.target.value);
                          setDiscountValue(Math.min(subtotal, Math.max(0, isNaN(v) ? 0 : v)));
                        }}
                        onBlur={() => setDiscountValue((v) => Math.min(Math.max(0, v), subtotal))}
                        className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-green-500 focus:border-green-500"
                        min="0"
                        max={subtotal}
                        placeholder="Nominal diskon"
                      />
                      {isTotalDiscountInvalid && (
                        <p className="mt-1 text-xs text-red-600">Nilai diskon total melebihi subtotal</p>
                      )}
                    </div>
                  )}
                </div>

                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Alasan Pengajuan Diskon
                  </label>
                  <textarea
                    value={discountReason}
                    onChange={(e) => setDiscountReason(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded focus:ring-green-500 focus:border-green-500"
                    rows={3}
                    placeholder="Jelaskan alasan mengapa mitra mendapat diskon..."
                  />
                </div>
              </div>
            )}
          </div>

          {/* Summary */}
          {items.length > 0 && (
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Ringkasan Transaksi</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span className="font-medium">{formatCurrency(subtotal)}</span>
                </div>
                {requestDiscount && discountType === 'total' && (
                  <div className="flex justify-between text-orange-600">
                    <span>Diskon Total:</span>
                    <span>-{formatCurrency(discountValue)}</span>
                  </div>
                )}
                <div className="border-t pt-2 flex justify-between text-lg font-bold">
                  <span>Total:</span>
                  <span>{formatCurrency(totalAfterDiscount)}</span>
                </div>
                {paymentMethod === 'credit' && (
                  <p className="text-sm text-gray-600">
                    Jatuh tempo: {new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toLocaleDateString('id-ID')}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => {
                setSelectedMitra(null);
                setItems([]);
                setRequestDiscount(false);
                setDiscountValue(0);
                setDiscountReason('');
              }}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Reset
            </button>
            <button
              type="submit"
              disabled={isSubmitting || items.length === 0 || !selectedMitra || isTotalDiscountInvalid}
              className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? 'Memproses...' : 'Submit Transaksi'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TransactionForm;
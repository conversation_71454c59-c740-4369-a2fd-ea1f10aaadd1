<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Reward extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'category_id',
        'product_id',
        'target_quantity',
        'start_date',
        'end_date',
        'status',
        'terms_conditions',
        'reward_image',
        'reward_value',
        'created_by'
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'target_quantity' => 'integer',
        'reward_value' => 'decimal:2',
    ];


    protected $appends = ['status_name'];

    /**
     * Relasi ke Category
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Relasi ke Product (opsional)
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Relasi ke User (creator)
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * <PERSON><PERSON><PERSON> ke RewardProgress
     */
    public function progresses()
    {
        return $this->hasMany(RewardProgress::class);
    }

    /**
     * Scope untuk reward aktif
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope untuk reward yang sedang berjalan
     */
    public function scopeRunning($query)
    {
        return $query->where('status', 'active')
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now());
    }

    /**
     * Scope untuk owner tertentu
     */
    public function scopeForOwner($query, $ownerId)
    {
        return $query->whereHas('creator', function ($q) use ($ownerId) {
            $q->where(function ($ownerScope) use ($ownerId) {
                $ownerScope->where('id', $ownerId)
                    ->orWhere('owner_id', $ownerId);
            });
        });
    }

    /**
     * Accessor untuk status yang user-friendly
     */
    public function getStatusNameAttribute(): string
    {
        $statusNames = [
            'active' => 'Aktif',
            'inactive' => 'Tidak Aktif',
            'completed' => 'Selesai',
            'expired' => 'Kedaluwarsa'
        ];

        return $statusNames[$this->status] ?? $this->status;
    }

    /**
     * Accessor untuk durasi program
     */
    public function getDurationAttribute(): string
    {
        $start = Carbon::parse($this->start_date);
        $end = Carbon::parse($this->end_date);
        $days = $start->diffInDays($end) + 1;

        return "{$days} hari";
    }

    /**
     * Accessor untuk sisa hari
     */
    public function getRemainingDaysAttribute(): int
    {
        if ($this->status !== 'active') {
            return 0;
        }

        $today = Carbon::today();
        $endDate = Carbon::parse($this->end_date);

        if ($today->gt($endDate)) {
            return 0;
        }

        return $today->diffInDays($endDate) + 1;
    }

    /**
     * Cek apakah reward masih berlaku
     */
    public function isValid(): bool
    {
        if ($this->status !== 'active') {
            return false;
        }

        $today = Carbon::today();
        $startDate = Carbon::parse($this->start_date);
        $endDate = Carbon::parse($this->end_date);

        return $today->between($startDate, $endDate);
    }

    /**
     * Cek apakah reward sudah kedaluwarsa
     */
    public function isExpired(): bool
    {
        $today = Carbon::today();
        $endDate = Carbon::parse($this->end_date);

        return $today->gt($endDate);
    }

    /**
     * Get total mitra yang berpartisipasi
     */
    public function getTotalParticipantsAttribute(): int
    {
        return $this->progresses()->count();
    }

    /**
     * Get mitra yang sudah menyelesaikan reward
     */
    public function getCompletedParticipantsAttribute(): int
    {
        return $this->progresses()->where('status', 'completed')->count();
    }

    /**
     * Get total quantity yang sudah terjual untuk reward ini
     */
    public function getTotalSoldQuantityAttribute(): int
    {
        return $this->progresses()->sum('current_quantity');
    }

    /**
     * Get progress percentage keseluruhan
     */
    public function getOverallProgressAttribute(): float
    {
        if ($this->target_quantity <= 0) {
            return 0;
        }

        return min(100, ($this->total_sold_quantity / $this->target_quantity) * 100);
    }

    /**
     * Cek apakah produk tertentu eligible untuk reward ini
     */
    public function isEligibleProduct($productId): bool
    {
        // Jika ada product_id spesifik, harus match
        if ($this->product_id) {
            return $this->product_id == $productId;
        }

        // Jika tidak ada product_id, cek berdasarkan kategori
        $product = Product::find($productId);
        if (!$product) {
            return false;
        }

        return $product->category_id == $this->category_id;
    }

    /**
     * Get top performers untuk reward ini
     */
    public function getTopPerformers($limit = 10)
    {
        return $this->progresses()
            ->with(['mitra'])
            ->orderBy('current_quantity', 'desc')
            ->orderBy('progress_percentage', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Update status otomatis berdasarkan tanggal
     */
    public function updateStatusIfNeeded(): bool
    {
        $updated = false;

        if ($this->status === 'active' && $this->isExpired()) {
            $this->update(['status' => 'expired']);
            $updated = true;

            // Update semua progress yang belum selesai menjadi expired
            $this->progresses()
                ->where('status', 'in_progress')
                ->update(['status' => 'expired']);
        }

        return $updated;
    }

    /**
     * Get reward statistics
     */
    public function getStatistics(): array
    {
        return [
            'total_participants' => $this->total_participants,
            'completed_participants' => $this->completed_participants,
            'completion_rate' => $this->total_participants > 0
                ? round(($this->completed_participants / $this->total_participants) * 100, 2)
                : 0,
            'total_sold_quantity' => $this->total_sold_quantity,
            'overall_progress' => round($this->overall_progress, 2),
            'remaining_days' => $this->remaining_days,
            'is_valid' => $this->isValid(),
            'is_expired' => $this->isExpired()
        ];
    }

    /**
     * Boot method untuk event handling
     */
    protected static function boot()
    {
        parent::boot();

        // Event sebelum hapus
        static::deleting(function ($reward) {
            // Hapus semua progress terkait
            $reward->progresses()->delete();
        });
    }
}

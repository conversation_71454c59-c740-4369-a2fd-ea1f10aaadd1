import { WhatsAppMessage } from '../types';

// Use backend proxy to avoid CORS issues
const WHATSAPP_API_URL = "https://api.agricash.senusatour.com/api/whatsapp/send";
// Token and API key are kept server-side now

export const sendWhatsAppMessage = async (data: WhatsAppMessage): Promise<boolean> => {
  try {
    const payload: any = {
      receiptnumber: data.receiptnumber,
      message: data.message,
    };
    if (data.imageurl) payload.imageurl = data.imageurl;
    if (data.documenturl) payload.documenturl = data.documenturl;

    const response = await fetch(WHATSAPP_API_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });

    const result = await response.json();

    // Log the response for debugging
    console.log('WhatsApp API Response:', result);

    return result.RESULT === 'OK';
  } catch (error) {
    console.error('Error sending WhatsApp message:', error);
    return false;
  }
};

export const sendTransactionNotification = async (
  phoneNumber: string,
  transactionId: string,
  status: string,
  mitraName: string
): Promise<void> => {
  let message = '';

  switch (status) {
    case 'approved':
      message = `🎉 *TRANSAKSI DISETUJUI*\n\nHalo! Transaksi #${transactionId} untuk mitra ${mitraName} telah disetujui dan akan segera diproses.\n\nTerima kasih atas kepercayaan Anda!\n\n_Agricultural Cashier System_`;
      break;
    case 'shipped':
      message = `🚛 *PESANAN SEDANG DIKIRIM*\n\nUpdate: Pesanan transaksi #${transactionId} untuk mitra ${mitraName} sedang dalam perjalanan.\n\nMohon bersiap untuk menerima barang.\n\n_Agricultural Cashier System_`;
      break;
    case 'delivered':
      message = `✅ *PESANAN DITERIMA*\n\nKonfirmasi: Pesanan transaksi #${transactionId} untuk mitra ${mitraName} telah diterima dengan baik.\n\nTerima kasih atas kepercayaan Anda!\n\n_Agricultural Cashier System_`;
      break;
    case 'rejected':
      message = `❌ *TRANSAKSI DITOLAK*\n\nMaaf, transaksi #${transactionId} untuk mitra ${mitraName} tidak dapat disetujui.\n\nSilakan hubungi admin untuk informasi lebih lanjut.\n\n_Agricultural Cashier System_`;
      break;
    default:
      message = `📋 *UPDATE TRANSAKSI*\n\nTransaksi #${transactionId} untuk mitra ${mitraName}:\nStatus berubah menjadi: ${status}\n\n_Agricultural Cashier System_`;
  }

  await sendWhatsAppMessage({
    receiptnumber: phoneNumber,
    message: message
  });
};

export const sendDiscountApprovalNotification = async (
  phoneNumber: string,
  transactionId: string,
  status: string,
  reason?: string
): Promise<void> => {
  let message = '';

  if (status === 'approved') {
    message = `✅ *DISKON DISETUJUI*\n\nSelamat! Pengajuan diskon untuk transaksi #${transactionId} telah disetujui oleh Owner.\n\nSilakan lanjutkan proses transaksi melalui Admin Gudang.\n\n_Agricultural Cashier System_`;
  } else {
    message = `❌ *DISKON DITOLAK*\n\nMaaf, pengajuan diskon untuk transaksi #${transactionId} ditolak.\n\n*Alasan:* ${reason || 'Tidak memenuhi syarat'}\n\nSilakan hubungi Owner untuk informasi lebih lanjut.\n\n_Agricultural Cashier System_`;
  }

  await sendWhatsAppMessage({
    receiptnumber: phoneNumber,
    message: message
  });
};

export const sendOwnerDiscountRequestNotification = async (
  phoneNumber: string,
  transactionId: string,
  salesName: string,
  mitraName: string,
  discountValue: number,
  discountReason: string
): Promise<void> => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const message = `🔔 *PENGAJUAN DISKON BARU*\n\n*Sales:* ${salesName}\n*Mitra:* ${mitraName}\n*Transaksi:* #${transactionId}\n*Nilai Diskon:* ${formatCurrency(discountValue)}\n\n*Alasan:*\n${discountReason}\n\nSilakan cek sistem untuk melakukan approval.\n\n_Agricultural Cashier System_`;

  await sendWhatsAppMessage({
    receiptnumber: phoneNumber,
    message: message
  });
};

export const sendWarehouseApprovalNotification = async (
  phoneNumber: string,
  transactionId: string,
  mitraName: string,
  totalAmount: number
): Promise<void> => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const message = `📦 *TRANSAKSI BARU - PENDING APPROVAL*\n\n*Transaksi:* #${transactionId}\n*Mitra:* ${mitraName}\n*Total:* ${formatCurrency(totalAmount)}\n\nTransaksi siap untuk diproses. Silakan cek sistem untuk approval.\n\n_Agricultural Cashier System_`;

  await sendWhatsAppMessage({
    receiptnumber: phoneNumber,
    message: message
  });
};
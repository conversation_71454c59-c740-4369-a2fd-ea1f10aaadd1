<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Unit extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'content',
        'quantity',
        'base_unit',
        'description',
        'status',
        'created_by'
    ];

    protected $casts = [
        'quantity' => 'integer',
    ];

    protected $appends = [
        'status_name',
        'conversion_info',
        'display_text',
    ];

    /**
     * Re<PERSON>i ke User (creator)
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relasi ke Product
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Scope untuk unit aktif
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope untuk unit tidak aktif
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    /**
     * Accessor untuk nama lengkap satuan
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->name} ({$this->content})";
    }

    /**
     * Accessor untuk status yang user-friendly
     */
    public function getStatusNameAttribute(): string
    {
        $statusNames = [
            'active' => 'Aktif',
            'inactive' => 'Tidak Aktif'
        ];

        return $statusNames[$this->status] ?? $this->status;
    }

    /**
     * Accessor untuk informasi konversi
     */
    public function getConversionInfoAttribute(): string
    {
        return "1 {$this->name} = {$this->quantity} {$this->base_unit}";
    }

    /**
     * Method untuk konversi dari unit ini ke base unit
     */
    public function convertToBaseUnit(float $amount): float
    {
        return $amount * $this->quantity;
    }

    /**
     * Method untuk konversi dari base unit ke unit ini
     */
    public function convertFromBaseUnit(float $baseAmount): float
    {
        return $this->quantity > 0 ? $baseAmount / $this->quantity : 0;
    }

    /**
     * Cek apakah unit sedang digunakan oleh produk
     */
    public function isInUse(): bool
    {
        return $this->products()->count() > 0;
    }

    /**
     * Get jumlah produk yang menggunakan unit ini
     */
    public function getProductCountAttribute(): int
    {
        return $this->products()->count();
    }

    /**
     * Validasi sebelum hapus unit
     */
    public function canBeDeleted(): bool
    {
        return !$this->isInUse();
    }

    /**
     * Format untuk display di dropdown
     */
    public function getDisplayTextAttribute(): string
    {
        return "{$this->name} - {$this->content}";
    }

    /**
     * Scope untuk pencarian
     */
    public function scopeSearch($query, $search)
    {
        if ($search) {
            return $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('content', 'like', "%{$search}%")
                    ->orWhere('base_unit', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        return $query;
    }

    /**
     * Boot method untuk event handling
     */
    protected static function boot()
    {
        parent::boot();

        // Event sebelum hapus
        static::deleting(function ($unit) {
            if ($unit->isInUse()) {
                throw new \Exception('Cannot delete unit that is being used by products');
            }
        });
    }
}

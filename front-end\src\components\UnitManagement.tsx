import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Package, BarChart3, Download, ToggleLeft, ToggleRight } from 'lucide-react';
import { apiFetch } from '../services/api';

interface Unit {
  id: number;
  name: string;
  content: string;
  quantity: number;
  base_unit: string;
  description?: string;
  status: 'active' | 'inactive';
  status_name: string;
  display_text: string;
  conversion_info: string;
  product_count: number;
  can_be_deleted: boolean;
  creator: {
    id: number;
    name: string;
  };
  created_at: string;
}

interface UnitStats {
  total_units: number;
  active_units: number;
  inactive_units: number;
  units_in_use: number;
  unused_units: number;
  most_used_units: Array<{
    id: number;
    name: string;
    content: string;
    products_count: number;
  }>;
}

const UnitManagement: React.FC = () => {
  const [units, setUnits] = useState<Unit[]>([]);
  const [stats, setStats] = useState<UnitStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [showStats, setShowStats] = useState(false);
  const [editingUnit, setEditingUnit] = useState<Unit | null>(null);
  // const [selectedUnits, setSelectedUnits] = useState<number[]>([]);

  const [formData, setFormData] = useState({
    name: '',
    content: '',
    quantity: '',
    base_unit: '',
    description: '',
    status: 'active' as 'active' | 'inactive'
  });

  const [filters, setFilters] = useState({
    status: '',
    search: ''
  });

  useEffect(() => {
    fetchUnits();
    fetchStats();
  }, [filters]);

  const fetchUnits = async () => {
    try {
      const params = new URLSearchParams();

      if (filters.status) params.append('status', filters.status);
      if (filters.search) params.append('search', filters.search);

      const data = await apiFetch(`/api/units?${params.toString()}`);
      setUnits(data.data || []);
    } catch (error) {
      console.error('Error fetching units:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const data = await apiFetch('/api/units-stats');
      setStats(data);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const url = editingUnit ? `/api/units/${editingUnit.id}` : '/api/units';
      const method = editingUnit ? 'PUT' : 'POST';

      await apiFetch(url, {
        method,
        body: JSON.stringify({
          ...formData,
          quantity: parseInt(formData.quantity)
        })
      });

      await fetchUnits();
      await fetchStats();
      resetForm();
      alert(editingUnit ? 'Satuan berhasil diperbarui!' : 'Satuan berhasil dibuat!');
    } catch (error) {
      console.error('Error saving unit:', error);
      alert('Terjadi kesalahan saat menyimpan satuan');
    }
  };

  const handleEdit = (unit: Unit) => {
    setEditingUnit(unit);
    setFormData({
      name: unit.name,
      content: unit.content,
      quantity: unit.quantity.toString(),
      base_unit: unit.base_unit,
      description: unit.description || '',
      status: unit.status
    });
    setShowForm(true);
  };

  const handleDelete = async (id: number) => {
    const unit = units.find(u => u.id === id);
    if (!unit?.can_be_deleted) {
      alert('Satuan tidak dapat dihapus karena sedang digunakan oleh produk');
      return;
    }

    if (!confirm('Apakah Anda yakin ingin menghapus satuan ini?')) return;

    try {
      await apiFetch(`/api/units/${id}`, {
        method: 'DELETE'
      });

      await fetchUnits();
      await fetchStats();
      alert('Satuan berhasil dihapus!');
    } catch (error) {
      console.error('Error deleting unit:', error);
      alert('Terjadi kesalahan saat menghapus satuan');
    }
  };

  // Aksi bulk dinonaktifkan seiring penghapusan checklist
  // const handleBulkStatusUpdate = async (status: 'active' | 'inactive') => {
  //   ...
  // };

  const handleExport = async () => {
    try {
      const response = await apiFetch('/api/units/export', {
        responseType: 'blob'
      });

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `master_satuan_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting:', error);
      alert('Terjadi kesalahan saat export data');
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      content: '',
      quantity: '',
      base_unit: '',
      description: '',
      status: 'active'
    });
    setEditingUnit(null);
    setShowForm(false);
  };

  const getStatusProps = (status: any, statusName?: string) => {
    const normalized =
      typeof status === 'string'
        ? status.trim().toLowerCase()
        : status === 1 || status === true
          ? 'active'
          : status === 0 || status === false
            ? 'inactive'
            : String(status ?? '');

    const label =
      statusName && String(statusName).trim()
        ? statusName
        : normalized === 'active'
          ? 'Aktif'
          : normalized === 'inactive'
            ? 'Tidak Aktif'
            : normalized || '—';

    const className =
      normalized === 'active'
        ? 'bg-green-100 text-green-800'
        : normalized === 'inactive'
          ? 'bg-gray-100 text-gray-800'
          : 'bg-gray-100 text-gray-800';

    return { className, label };
  };

  // Checklist dan aksi bulk dihapus
  // const toggleUnitSelection = (unitId: number) => { ... };
  // const toggleSelectAll = () => { ... };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Master Satuan</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowStats(!showStats)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
          >
            <BarChart3 className="h-4 w-4" />
            <span>{showStats ? 'Sembunyikan' : 'Lihat'} Statistik</span>
          </button>
          <button
            onClick={handleExport}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
          >
            <Download className="h-4 w-4" />
            <span>Export CSV</span>
          </button>
          <button
            onClick={() => setShowForm(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Tambah Satuan</span>
          </button>
        </div>
      </div>

      {/* Statistics */}
      {showStats && stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Satuan</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.total_units}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <ToggleRight className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Satuan Aktif</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.active_units}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <BarChart3 className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Sedang Digunakan</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.units_in_use}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-gray-100 rounded-lg">
                <ToggleLeft className="h-6 w-6 text-gray-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Tidak Digunakan</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.unused_units}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            >
              <option value="">Semua Status</option>
              <option value="active">Aktif</option>
              <option value="inactive">Tidak Aktif</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Pencarian</label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              placeholder="Cari nama satuan, isi, atau satuan dasar..."
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            />
          </div>

          <div className="flex items-end space-x-2">
            {/* Aksi bulk dinonaktifkan seiring penghapusan checklist */}
          </div>
        </div>
      </div>

      {/* Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingUnit ? 'Edit Satuan' : 'Tambah Satuan Baru'}
              </h3>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Nama Satuan</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="dus, karton, box, dll"
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Isi dalam Satuan</label>
                  <input
                    type="text"
                    value={formData.content}
                    onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                    placeholder="12 botol, 24 pcs, dll"
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Jumlah (Angka)</label>
                    <input
                      type="number"
                      value={formData.quantity}
                      onChange={(e) => setFormData({ ...formData, quantity: e.target.value })}
                      placeholder="12, 24, dll"
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      min="1"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Satuan Dasar</label>
                    <input
                      type="text"
                      value={formData.base_unit}
                      onChange={(e) => setFormData({ ...formData, base_unit: e.target.value })}
                      placeholder="botol, pcs, kg, liter, dll"
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Deskripsi (Opsional)</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    rows={3}
                    placeholder="Deskripsi tambahan tentang satuan ini..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value as 'active' | 'inactive' })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="active">Aktif</option>
                    <option value="inactive">Tidak Aktif</option>
                  </select>
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Batal
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    {editingUnit ? 'Perbarui' : 'Simpan'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Units Table */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Daftar Satuan</h3>

          {units.length === 0 ? (
            <div className="text-center py-8">
              <Package className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Belum ada satuan</h3>
              <p className="mt-1 text-sm text-gray-500">Mulai dengan membuat satuan pertama Anda.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Satuan
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Konversi
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Penggunaan
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Aksi
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {units.map((unit) => (
                    <tr key={unit.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{unit.name}</div>
                          <div className="text-sm text-gray-500">{unit.content}</div>
                          {unit.description && (
                            <div className="text-xs text-gray-400 mt-1">{unit.description}</div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{unit.conversion_info || unit.display_text}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {unit.product_count} produk
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {(() => {
                          const { className, label } = getStatusProps(unit.status, unit.status_name); return (
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${className}`}>{label}</span>
                          );
                        })()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEdit(unit)}
                            className="text-indigo-600 hover:text-indigo-900"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          {unit.can_be_deleted && (
                            <button
                              onClick={() => handleDelete(unit.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UnitManagement;

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\Setting;
use Spipu\Html2Pdf\Html2Pdf;

class ProfitLossController extends Controller
{
    private array $salesStatuses = ['approved', 'shipped', 'delivered'];

    // GET /api/reports/profit-loss?start_date=YYYY-MM-DD&end_date=YYYY-MM-DD
    public function profitLoss(Request $request)
    {
        $request->validate([
            'start_date' => ['nullable', 'date'],
            'end_date' => ['nullable', 'date', 'after_or_equal:start_date'],
        ]);

        $user = $request->user();
        $role = $user?->role ?? null;

        $start = $request->query('start_date') ? Carbon::parse($request->query('start_date'))->startOfDay() : Carbon::now()->startOfMonth();
        $end = $request->query('end_date') ? Carbon::parse($request->query('end_date'))->endOfDay() : Carbon::now()->endOfDay();

        $tx = DB::table('transactions as t')
            ->leftJoin('transaction_items as ti', 'ti.transaction_id', '=', 't.id')
            ->leftJoin('users as s', 't.sales_id', '=', 's.id')
            ->leftJoin('mitras as m', 't.mitra_id', '=', 'm.id')
            ->select(
                DB::raw('COALESCE(SUM(ti.total), 0) as revenue'),
                DB::raw('COALESCE(SUM(ti.hpp_at_sale * ti.quantity), 0) as cogs')
            )
            ->whereBetween('t.created_at', [$start, $end])
            ->whereIn('t.status', $this->salesStatuses);

        // Scope by owner for non-superadmin (owner, admin_gudang, sales)
        if ($user && $role !== 'superadmin') {
            $ownerScopeId = $role === 'owner' ? $user->id : ($user->owner_id ?? null);
            if ($ownerScopeId) {
                $tx->where(function ($qq) use ($ownerScopeId) {
                    $qq->where('s.owner_id', $ownerScopeId)
                        ->orWhere('m.owner_id', $ownerScopeId);
                });
            }
        }

        $row = $tx->first();
        $revenue = (int) ($row->revenue ?? 0);
        $cogs = (int) ($row->cogs ?? 0);
        $grossProfit = $revenue - $cogs;
        $grossMargin = $revenue > 0 ? round(($grossProfit / $revenue) * 100, 2) : 0;

        return response()->json([
            'period' => [
                'start' => $start->toDateString(),
                'end' => $end->toDateString(),
            ],
            'revenue' => $revenue,
            'cogs' => $cogs,
            'gross_profit' => $grossProfit,
            'gross_margin_percent' => $grossMargin,
        ]);
    }


    // GET /api/reports/profit-loss.pdf?start_date=YYYY-MM-DD&end_date=YYYY-MM-DD
    public function profitLossPdf(Request $request)
    {
        $request->validate([
            'start_date' => ['nullable', 'date'],
            'end_date' => ['nullable', 'date', 'after_or_equal:start_date'],
        ]);

        // Reuse logic from profitLoss() to compute numbers
        $json = $this->profitLoss($request);
        $payload = $json->getData(true);

        // Resolve settings for header/logo
        $setting = Setting::first();
        $data = [
            'biz' => $setting->business_name ?? 'Laporan Laba Rugi',
            'addr' => $setting->address ?? '',
            'logo' => $setting->logo_url ?? null,
            'period' => $payload['period'] ?? null,
            'revenue' => $payload['revenue'] ?? 0,
            'cogs' => $payload['cogs'] ?? 0,
            'gross_profit' => $payload['gross_profit'] ?? 0,
            'gross_margin_percent' => $payload['gross_margin_percent'] ?? 0,
            'now' => now(),
        ];

        // Fetch detailed transactions within the period for PDF table
        $start = isset($data['period']['start']) ? Carbon::parse($data['period']['start'])->startOfDay() : Carbon::now()->startOfMonth();
        $end = isset($data['period']['end']) ? Carbon::parse($data['period']['end'])->endOfDay() : Carbon::now()->endOfDay();

        $user = $request->user();
        $role = $user?->role ?? null;

        $list = DB::table('transactions as t')
            ->leftJoin('transaction_items as ti', 'ti.transaction_id', '=', 't.id')
            ->leftJoin('users as s', 't.sales_id', '=', 's.id')
            ->leftJoin('mitras as m', 't.mitra_id', '=', 'm.id')
            ->whereBetween('t.created_at', [$start, $end])
            ->whereIn('t.status', $this->salesStatuses);

        if ($user && $role !== 'superadmin') {
            $ownerScopeId = $role === 'owner' ? $user->id : ($user->owner_id ?? null);
            if ($ownerScopeId) {
                $list->where(function ($qq) use ($ownerScopeId) {
                    $qq->where('s.owner_id', $ownerScopeId)
                        ->orWhere('m.owner_id', $ownerScopeId);
                });
            }
        }

        $rows = $list
            ->select(
                't.id',
                't.created_at',
                't.status',
                DB::raw('COALESCE(m.name, "-") as mitra_name'),
                DB::raw('COALESCE(s.name, "-") as sales_name'),
                DB::raw('COALESCE(SUM(ti.total), 0) as revenue'),
                DB::raw('COALESCE(SUM(ti.hpp_at_sale * ti.quantity), 0) as cogs')
            )
            ->groupBy('t.id', 't.created_at', 't.status', 'm.name', 's.name')
            ->orderBy('t.created_at', 'asc')
            ->get();

        $data['transactions'] = $rows->map(function ($r) {
            $rev = (int) $r->revenue;
            $cogs = (int) $r->cogs;
            return [
                'id' => $r->id,
                'date' => Carbon::parse($r->created_at)->format('d/m/Y H:i'),
                'status' => $r->status,
                'mitra' => $r->mitra_name,
                'sales' => $r->sales_name,
                'revenue' => $rev,
                'cogs' => $cogs,
                'gross' => $rev - $cogs,
            ];
        })->toArray();

        // Convert logo URL to file path if it's in storage
        $logoPath = null;
        if (!empty($data['logo']) && strpos($data['logo'], '/storage/') !== false) {
            $relativePath = substr($data['logo'], strpos($data['logo'], '/storage/') + 9);
            $absolutePath = storage_path('app/public/' . $relativePath);
            if (file_exists($absolutePath)) {
                $logoPath = $absolutePath;
            }
        }
        $data['logo'] = $logoPath;

        $html = view('pdf.profit_loss', $data)->render();
        // Landscape orientation to provide more horizontal room for tables
        $pdf = new Html2Pdf('L', 'A4', 'en', true, 'UTF-8', [8, 10, 8, 10]);
        $pdf->setDefaultFont('Helvetica');
        $pdf->pdf->SetTitle('Laporan Laba Rugi');
        $pdf->writeHTML($html);

        $content = $pdf->output('laporan-laba-rugi.pdf', 'S');
        return response($content, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="laporan-laba-rugi.pdf"',
        ]);
    }
}

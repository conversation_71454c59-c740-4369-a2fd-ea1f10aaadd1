<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: Helvetica, Arial, sans-serif;
            font-size: 12px;
            color: #333;
        }

        .header {
            display: flex;
            align-items: center;
            border-bottom: 1px solid #ddd;
            padding-bottom: 8px;
            margin-bottom: 12px;
        }

        .logo {
            width: 60px;
            height: 60px;
            object-fit: contain;
            margin-right: 12px;
        }

        .title {
            font-size: 18px;
            font-weight: bold;
        }

        .subtitle {
            font-size: 12px;
            color: #666;
        }

        /* Avoid CSS Grid for PDF compatibility */
        .label {
            font-size: 11px;
            color: #666;
            margin-bottom: 2px;
        }

        .value {
            font-size: 14px;
            font-weight: bold;
        }

        .nowrap {
            white-space: nowrap;
        }

        .table {
            width: auto;
            /* avoid percentage width for better PDF rendering */
            border-collapse: collapse;
            margin-top: 12px;
        }

        .table th,
        .table td {
            padding: 8px;
            background: #ffffff;
            /* ensure text not bleeding through */
            border: 1px solid #eee;
            text-align: left;
        }

        .table th {
            background: #f8f8f8;
            font-weight: bold;
        }

        .text-right {
            text-align: right;
        }

        .muted {
            color: #666;
            font-size: 11px;
        }

        .mt-16 {
            margin-top: 16px;
        }

        .mt-24 {
            margin-top: 24px;
        }
    </style>
</head>

<body>
    <div class="header">
        @if (!empty($logo))
            <img src="{{ $logo }}" alt="Logo" class="logo">
        @endif
        <div>
            <div class="title">Laporan Laba Rugi</div>
            <div class="subtitle">{{ strtoupper($biz ?? 'AGRIGRO') }}@if (!empty($addr))
                    — {{ $addr }}
                @endif
            </div>
            <div class="subtitle">Periode: {{ $period['start'] ?? '' }} s/d {{ $period['end'] ?? '' }}</div>
        </div>
    </div>

    <table class="table" style="width:auto;">
        <tbody>
            <tr>
                <td style="width: 260px;">
                    <div class="label">Penjualan (Revenue)</div>
                    <div class="value nowrap">Rp {{ number_format((int) ($revenue ?? 0), 0, ',', '.') }}</div>
                </td>
                <td style="width: 260px;">
                    <div class="label">HPP (COGS)</div>
                    <div class="value nowrap">Rp {{ number_format((int) ($cogs ?? 0), 0, ',', '.') }}</div>
                </td>
            </tr>
            <tr>
                <td style="width: 260px;">
                    <div class="label">Laba Kotor</div>
                    <div class="value nowrap">Rp {{ number_format((int) ($gross_profit ?? 0), 0, ',', '.') }}</div>
                </td>
                <td style="width: 260px;">
                    <div class="label">Gross Margin</div>
                    <div class="value nowrap">{{ number_format((float) ($gross_margin_percent ?? 0), 2, ',', '.') }}%
                    </div>
                </td>
            </tr>
        </tbody>
    </table>



    <div style="margin-top: 12px; font-size: 10px; color: #888;">Dicetak: {{ optional($now)->format('d/m/Y H:i') }}
    </div>

    @if (!empty($transactions))
        <h3 class="mt-24" style="font-size:14px; font-weight:bold;">Rincian Transaksi</h3>
        <table class="table" style="width:auto;">
            <thead>
                <tr>
                    <th style="width: 40px;">ID</th>
                    <th style="width: 120px;">Tanggal</th>
                    <th style="width: 220px;">Mitra</th>
                    <th style="width: 160px;">Sales</th>
                    <th class="text-right" style="width: 100px;">Revenue</th>
                    <th class="text-right" style="width: 90px;">HPP</th>
                    <th class="text-right" style="width: 100px;">Laba Kotor</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($transactions as $t)
                    <tr>
                        <td class="nowrap">{{ $t['id'] }}</td>
                        <td class="nowrap">{{ $t['date'] }}</td>
                        <td>{{ $t['mitra'] }}</td>
                        <td>{{ $t['sales'] }}</td>
                        <td class="text-right nowrap">Rp {{ number_format((int) $t['revenue'], 0, ',', '.') }}</td>
                        <td class="text-right nowrap">Rp {{ number_format((int) $t['cogs'], 0, ',', '.') }}</td>
                        <td class="text-right nowrap">Rp {{ number_format((int) $t['gross'], 0, ',', '.') }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
        <div class="muted mt-16">Total transaksi: {{ count($transactions) }}</div>
    @endif


</body>

</html>
